import 'package:wargani/features/authentication/domain/entities/user_entity.dart';
import 'package:wargani/core/utils/result.dart';

/// Interface for authentication repository
abstract class AuthRepositoryInterface {
  /// Login with email and password
  Future<Result<UserEntity>> login(String email, String password);
  
  /// Register a new user
  Future<Result<UserEntity>> register(UserEntity user);
  
  /// Logout current user
  Future<Result<void>> logout();
  
  /// Get current logged in user
  Future<Result<UserEntity?>> getCurrentUser();
  
  /// Reset password using secret question
  Future<Result<void>> resetPassword(String email, String newPassword, String secretAnswer);
  
  /// Check if user is logged in
  Future<Result<bool>> isUserLoggedIn();
}
