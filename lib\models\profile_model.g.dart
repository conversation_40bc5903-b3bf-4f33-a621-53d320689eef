// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProfileAdapter extends TypeAdapter<Profile> {
  @override
  final int typeId = 0;

  @override
  Profile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Profile(
      mandalName: fields[0] as String,
      address: fields[1] as String,
      logoBytes: fields[2] as Uint8List?,
      currentYear: fields[3] as String,
      mandalRegistrationNo: fields[4] as String?,
      festivalName: fields[5] as String?,
      leftLogoBytes: fields[6] as Uint8List?,
      rightLogoBytes: fields[7] as Uint8List?,
      customHeaderText: fields[8] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Profile obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.mandalName)
      ..writeByte(1)
      ..write(obj.address)
      ..writeByte(2)
      ..write(obj.logoBytes)
      ..writeByte(3)
      ..write(obj.currentYear)
      ..writeByte(4)
      ..write(obj.mandalRegistrationNo)
      ..writeByte(5)
      ..write(obj.festivalName)
      ..writeByte(6)
      ..write(obj.leftLogoBytes)
      ..writeByte(7)
      ..write(obj.rightLogoBytes)
      ..writeByte(8)
      ..write(obj.customHeaderText);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProfileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
