import 'package:flutter/material.dart';
import 'package:wargani/fixed_marathi_receipt_generator.dart';

/// Test app for Fixed Marathi Receipt Generator
/// 
/// This app demonstrates the generation of Marathi receipts with
/// HARDCODED labels that never change, ensuring consistency

class TestFixedMarathiReceiptApp extends StatelessWidget {
  const TestFixedMarathiReceiptApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Fixed मराठी पावती',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        fontFamily: 'Roboto',
      ),
      home: const FixedMarathiReceiptScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// Main function to run the test app
void main() {
  runApp(const TestFixedMarathiReceiptApp());
}

/// Standalone function to generate sample Fixed Marathi receipt
/// 
/// Usage:
/// ```dart
/// final pdfPath = await generateSampleFixedMarathiReceipt();
/// print('Fixed receipt generated at: $pdfPath');
/// ```
Future<String?> generateSampleFixedMarathiReceipt() async {
  try {
    print('🧾 Generating sample Fixed Marathi receipt...');
    
    final pdfPath = await FixedMarathiReceiptGenerator.generateFixedMarathiReceipt(
      amount: 4425.0,
      date: DateTime.now(),
      donorName: 'श्री राम शर्मा',
      receiptNumber: 1001,
      currentYear: '2025',
    );
    
    if (pdfPath != null) {
      print('✅ Sample fixed receipt generated successfully at: $pdfPath');
    } else {
      print('✅ Sample fixed receipt opened in browser (Web platform)');
    }
    
    return pdfPath;
    
  } catch (e) {
    print('❌ Error generating sample fixed receipt: $e');
    rethrow;
  }
}

/// Test function to verify all FIXED labels are correctly hardcoded
void testFixedLabels() {
  print('🔒 Testing FIXED labels (these should NEVER change)...');
  
  final fixedLabels = [
    FixedMarathiReceiptGenerator.FIXED_TITLE,
    FixedMarathiReceiptGenerator.FIXED_FESTIVAL,
    FixedMarathiReceiptGenerator.FIXED_RECEIPT_NO,
    FixedMarathiReceiptGenerator.FIXED_CASH_RECEIVED,
    FixedMarathiReceiptGenerator.FIXED_IN_WORDS,
    FixedMarathiReceiptGenerator.FIXED_DATE,
    FixedMarathiReceiptGenerator.FIXED_FROM_SUFFIX,
    FixedMarathiReceiptGenerator.FIXED_THANK_YOU,
    FixedMarathiReceiptGenerator.FIXED_SIGNATURE,
    FixedMarathiReceiptGenerator.FIXED_DEVELOPER,
    FixedMarathiReceiptGenerator.FIXED_CASH_RECEIVED_FOOTER,
  ];
  
  print('📝 FIXED labels verification:');
  for (int i = 0; i < fixedLabels.length; i++) {
    print('${i + 1}. "${fixedLabels[i]}"');
  }
  
  print('✅ All FIXED labels verified. These will appear exactly as shown in every receipt.');
}

/// Test function to verify dynamic fields work correctly
void testDynamicFields() {
  print('📝 Testing DYNAMIC fields (only these can change)...');
  
  final testCases = [
    {
      'amount': 1000.0,
      'donorName': 'श्री गणेश पाटील',
      'receiptNumber': 1001,
      'year': '2025',
    },
    {
      'amount': 4425.0,
      'donorName': 'श्रीमती सीता देवी',
      'receiptNumber': 1002,
      'year': '2025',
    },
    {
      'amount': 10000.0,
      'donorName': 'श्री राम शर्मा',
      'receiptNumber': 1003,
      'year': '2025',
    },
  ];
  
  print('📝 Dynamic field test cases:');
  for (int i = 0; i < testCases.length; i++) {
    final testCase = testCases[i];
    print('${i + 1}. Amount: ₹${testCase['amount']}, '
          'Donor: ${testCase['donorName']}, '
          'Receipt: ${testCase['receiptNumber']}, '
          'Year: ${testCase['year']}');
  }
  
  print('✅ Dynamic field tests prepared. Generate receipts to verify.');
}

/// Test function to verify conjunct character rendering in fixed labels
void testConjunctCharactersInFixedLabels() {
  print('🧪 Testing conjunct characters in FIXED labels...');
  
  final conjunctTests = [
    'श्री गणेश प्रसन्न - contains श्री and प्रसन्न conjuncts',
    'गणेशोत्सव - contains conjunct characters',
    'यांच्याकडून - contains conjunct characters',
    'धन्यवाद - contains न्य conjunct',
    'रोख मिळाले - standard Marathi text',
  ];
  
  print('📝 Conjunct character tests in fixed labels:');
  for (int i = 0; i < conjunctTests.length; i++) {
    print('${i + 1}. ${conjunctTests[i]}');
  }
  
  print('✅ All conjunct tests prepared. Generate receipt to verify rendering.');
}

/// Quick test widget for standalone testing
class QuickFixedReceiptTestWidget extends StatefulWidget {
  const QuickFixedReceiptTestWidget({super.key});

  @override
  State<QuickFixedReceiptTestWidget> createState() => _QuickFixedReceiptTestWidgetState();
}

class _QuickFixedReceiptTestWidgetState extends State<QuickFixedReceiptTestWidget> {
  String _testResults = 'Ready to run Fixed Receipt tests';
  bool _isGenerating = false;

  void _runFixedLabelTests() {
    setState(() {
      _testResults = 'Running FIXED label tests...';
    });

    testFixedLabels();

    setState(() {
      _testResults = '✅ FIXED label tests completed. Check console for details.';
    });
  }

  void _runDynamicFieldTests() {
    setState(() {
      _testResults = 'Running dynamic field tests...';
    });

    testDynamicFields();

    setState(() {
      _testResults = '✅ Dynamic field tests completed. Check console for details.';
    });
  }

  void _runConjunctTests() {
    setState(() {
      _testResults = 'Running conjunct character tests...';
    });

    testConjunctCharactersInFixedLabels();

    setState(() {
      _testResults = '✅ Conjunct character tests completed. Check console for details.';
    });
  }

  Future<void> _generateSampleFixedReceipt() async {
    setState(() {
      _testResults = 'Generating sample Fixed receipt...';
      _isGenerating = true;
    });

    try {
      final pdfPath = await generateSampleFixedMarathiReceipt();
      setState(() {
        _testResults = pdfPath != null 
            ? '✅ Sample Fixed receipt generated: $pdfPath'
            : '✅ Sample Fixed receipt opened in browser';
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _testResults = '❌ Sample Fixed receipt generation failed: $e';
        _isGenerating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fixed Marathi Receipt Test'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Fixed मराठी पावती जनरेटर टेस्ट',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Test Results:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(_testResults),
            ),
            
            const SizedBox(height: 30),
            
            // Test buttons
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _runFixedLabelTests,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.all(15),
                ),
                child: const Text(
                  'Test FIXED Labels',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _runDynamicFieldTests,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  padding: const EdgeInsets.all(15),
                ),
                child: const Text(
                  'Test Dynamic Fields',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _runConjunctTests,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo,
                  padding: const EdgeInsets.all(15),
                ),
                child: const Text(
                  'Test Conjunct Characters',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isGenerating ? null : _generateSampleFixedReceipt,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.all(15),
                ),
                child: _isGenerating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(color: Colors.white),
                          ),
                          SizedBox(width: 10),
                          Text('Generating...', style: TextStyle(color: Colors.white)),
                        ],
                      )
                    : const Text(
                        'Generate Sample Fixed Receipt',
                        style: TextStyle(color: Colors.white),
                      ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Instructions
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            const Text(
              '1. Test FIXED labels to verify they never change\n'
              '2. Test dynamic fields to verify they work correctly\n'
              '3. Test conjunct characters in fixed labels\n'
              '4. Generate sample receipt to verify PDF output\n'
              '5. Verify all FIXED labels appear exactly as specified',
              style: TextStyle(fontSize: 14),
            ),
            
            const SizedBox(height: 20),
            
            // Fixed labels display
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🔒 FIXED Labels (Never Change):',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                  SizedBox(height: 5),
                  Text(
                    '• || श्री गणेश प्रसन्न ||\n'
                    '• गणेशोत्सव\n'
                    '• पावती नं.\n'
                    '• रोख मिळाले:\n'
                    '• अक्षरी:\n'
                    '• दिनांक:\n'
                    '• यांच्याकडून\n'
                    '• धन्यवाद...!\n'
                    '• सही\n'
                    '• रोख मिळाले...!\n'
                    '• Developed by AMSSoftX Web: https://amsssoftx.com',
                    style: TextStyle(fontSize: 12, color: Colors.orange),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Instructions for using this test:
/// 
/// 1. **Run the main app:**
///    ```bash
///    flutter run lib/test_fixed_marathi_receipt.dart
///    ```
/// 
/// 2. **Generate sample receipt programmatically:**
///    ```dart
///    final pdfPath = await generateSampleFixedMarathiReceipt();
///    ```
/// 
/// 3. **What to verify in the generated receipt:**
///    - ✅ Title: || श्री गणेश प्रसन्न || (EXACTLY as specified)
///    - ✅ All FIXED labels appear exactly as hardcoded
///    - ✅ Dynamic fields (amount, date, donor name) change correctly
///    - ✅ Conjunct characters render properly
///    - ✅ Amount in Marathi words converts correctly
///    - ✅ Developer credit appears at bottom
/// 
/// 4. **Expected receipt structure:**
///    ```
///    || श्री गणेश प्रसन्न ||
///    गणेशोत्सव 2025
///    
///    पावती नं. 1001        दिनांक: 18/07/2025
///    
///    सो.श्री राम शर्मा यांच्याकडून
///    
///    रोख मिळाले: ₹ 4425
///    
///    अक्षरी: चार हजार चारशे पंचवीस रुपये फक्त
///    
///    धन्यवाद...!          रोख मिळाले...!
///    सही
///    
///    Developed by AMSSoftX Web: https://amsssoftx.com
///    ```
/// 
/// 5. **Critical verification points:**
///    - ✅ NO labels should be translated or changed
///    - ✅ ALL fixed text appears in Marathi exactly as specified
///    - ✅ Unicode Devanagari font renders all characters correctly
///    - ✅ PDF is print-ready with embedded fonts
