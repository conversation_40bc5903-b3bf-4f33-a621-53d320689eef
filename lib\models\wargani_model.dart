import 'package:hive/hive.dart';

part 'wargani_model.g.dart';

@HiveType(typeId: 1)
class Wargani extends HiveObject {
  @HiveField(0)
  int receiptNo;

  @HiveField(1)
  String name;

  @HiveField(2)
  double amount;

  @HiveField(3)
  DateTime date;

  @HiveField(4)
  String prefix;

  @HiveField(5) // New field for mobile number
  String? mobileNo;

  @HiveField(6)
  String? registrationNo;

  @HiveField(7)
  String? amountInWords;

  Wargani({
    required this.receiptNo,
    required this.name,
    required this.amount,
    required this.date,
    required this.prefix,
    this.mobileNo, // Make mobileNo optional
    this.registrationNo,
    this.amountInWords,
  });
}
