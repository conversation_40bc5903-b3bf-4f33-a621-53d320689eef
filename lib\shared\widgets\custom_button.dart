import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';

/// Professional custom button with multiple variants
class CustomButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonVariant variant;
  final ButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final Color? backgroundColor;
  final Color? textColor;
  final double? borderRadius;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
  });

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.shortAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildButton(context),
        );
      },
    );
  }

  Widget _buildButton(BuildContext context) {
    final buttonStyle = _getButtonStyle(context);
    final textStyle = _getTextStyle(context);
    final padding = _getPadding();

    Widget buttonChild = _buildButtonContent(textStyle);

    if (widget.isFullWidth) {
      buttonChild = SizedBox(
        width: double.infinity,
        child: buttonChild,
      );
    }

    switch (widget.variant) {
      case ButtonVariant.primary:
        return ElevatedButton(
          onPressed: widget.isLoading ? null : widget.onPressed,
          style: buttonStyle,
          onLongPress: _handleLongPress,
          child: buttonChild,
        );
      case ButtonVariant.secondary:
        return OutlinedButton(
          onPressed: widget.isLoading ? null : widget.onPressed,
          style: buttonStyle,
          onLongPress: _handleLongPress,
          child: buttonChild,
        );
      case ButtonVariant.text:
        return TextButton(
          onPressed: widget.isLoading ? null : widget.onPressed,
          style: buttonStyle,
          onLongPress: _handleLongPress,
          child: buttonChild,
        );
    }
  }

  Widget _buildButtonContent(TextStyle textStyle) {
    if (widget.isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.textColor ?? Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text('Loading...', style: textStyle),
        ],
      );
    }

    if (widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.icon,
            size: _getIconSize(),
            color: widget.textColor,
          ),
          const SizedBox(width: 8),
          Text(widget.text, style: textStyle),
        ],
      );
    }

    return Text(widget.text, style: textStyle);
  }

  ButtonStyle _getButtonStyle(BuildContext context) {
    final colorScheme = context.colorScheme;
    
    Color backgroundColor;
    Color foregroundColor;
    BorderSide? borderSide;

    switch (widget.variant) {
      case ButtonVariant.primary:
        backgroundColor = widget.backgroundColor ?? colorScheme.primary;
        foregroundColor = widget.textColor ?? colorScheme.onPrimary;
        break;
      case ButtonVariant.secondary:
        backgroundColor = Colors.transparent;
        foregroundColor = widget.textColor ?? colorScheme.primary;
        borderSide = BorderSide(color: colorScheme.primary);
        break;
      case ButtonVariant.text:
        backgroundColor = Colors.transparent;
        foregroundColor = widget.textColor ?? colorScheme.primary;
        break;
    }

    return ButtonStyle(
      backgroundColor: WidgetStateProperty.all(backgroundColor),
      foregroundColor: WidgetStateProperty.all(foregroundColor),
      padding: WidgetStateProperty.all(_getPadding()),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? AppConstants.borderRadius,
          ),
        ),
      ),
      side: borderSide != null ? WidgetStateProperty.all(borderSide) : null,
      elevation: widget.variant == ButtonVariant.primary
          ? WidgetStateProperty.all(2)
          : WidgetStateProperty.all(0),
    );
  }

  TextStyle _getTextStyle(BuildContext context) {
    final textTheme = context.textTheme;
    
    switch (widget.size) {
      case ButtonSize.small:
        return textTheme.bodySmall!.copyWith(
          fontWeight: FontWeight.w600,
          color: widget.textColor,
        );
      case ButtonSize.medium:
        return textTheme.bodyMedium!.copyWith(
          fontWeight: FontWeight.w600,
          color: widget.textColor,
        );
      case ButtonSize.large:
        return textTheme.bodyLarge!.copyWith(
          fontWeight: FontWeight.w600,
          color: widget.textColor,
        );
    }
  }

  EdgeInsets _getPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }

  void _handleLongPress() {
    if (!_isPressed) {
      _isPressed = true;
      _animationController.forward().then((_) {
        _animationController.reverse().then((_) {
          _isPressed = false;
        });
      });
    }
  }
}

enum ButtonVariant {
  primary,
  secondary,
  text,
}

enum ButtonSize {
  small,
  medium,
  large,
}
