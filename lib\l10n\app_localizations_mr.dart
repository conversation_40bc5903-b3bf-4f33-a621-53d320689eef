// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Marathi (`mr`).
class AppLocalizationsMr extends AppLocalizations {
  AppLocalizationsMr([String locale = 'mr']) : super(locale);

  @override
  String get appTitle => 'वारगणी';

  @override
  String get developedBy => 'विकसित केले आहे AMSSoftX | https://amssoftx.com';

  @override
  String get profile => 'प्रोफाइल';

  @override
  String get mandalName => 'मंडळाचं नाव';

  @override
  String get address => 'पत्ता';

  @override
  String get logo => 'लोगो';

  @override
  String get currentYear => 'कार्यकाल';

  @override
  String get save => 'सेव्ह करा';

  @override
  String get dashboard => 'डॅशबोर्ड';

  @override
  String get totalWargani => 'एकूण वर्गणी';

  @override
  String get totalDonations => 'एकूण देणगी';

  @override
  String get totalExpenses => 'एकूण खर्च';

  @override
  String get warganiReceipt => 'वर्गणी पावती';

  @override
  String get receiptNo => 'पावती क्र.';

  @override
  String get date => 'दिनांक';

  @override
  String get name => 'नाव';

  @override
  String get amount => 'रक्कम';

  @override
  String get amountInWords => 'अक्षरी रक्कम';

  @override
  String get generatePdf => 'PDF तयार करा';

  @override
  String get share => 'शेअर करा';

  @override
  String get download => 'डाउनलोड करा';

  @override
  String get thankYouNote => 'वर्गणी दिल्याबद्दल धन्यवाद.';

  @override
  String get expenses => 'खर्च';

  @override
  String get title => 'शीर्षक';

  @override
  String get description => 'वर्णन';

  @override
  String get addExpense => 'खर्च टाका';

  @override
  String get donations => 'देणगी';

  @override
  String get donorName => 'देणगीदाराचे नाव';

  @override
  String get reason => 'कारण (ऐच्छिक)';

  @override
  String get addDonation => 'देणगी टाका';

  @override
  String get prefix => 'उपपद (उदा. श्री./सौ.)';

  @override
  String get warganiSummary => 'वर्गणी सारांश';

  @override
  String get donationSummary => 'देणगी सारांश';

  @override
  String get expensesSummary => 'खर्च सारांश';

  @override
  String get totalPeople => 'एकूण व्यक्ती';

  @override
  String get totalAmount => 'एकूण रक्कम';

  @override
  String get login => 'लॉगिन करा';

  @override
  String get email => 'ईमेल';

  @override
  String get password => 'पासवर्ड';

  @override
  String get pleaseEnterEmail => 'कृपया तुमचा ईमेल टाका';

  @override
  String get pleaseEnterPassword => 'कृपया तुमचा पासवर्ड टाका';

  @override
  String get noUserFound => 'या ईमेलसाठी कोणताही वापरकर्ता आढळला नाही.';

  @override
  String get wrongPassword => 'त्या वापरकर्त्यासाठी चुकीचा पासवर्ड दिला आहे.';

  @override
  String loginFailed(Object errorMessage) {
    return 'लॉगिन अयशस्वी झाले: $errorMessage';
  }

  @override
  String get appName => 'वारगणी';

  @override
  String get dontHaveAccount => 'खाते नाही? साइन अप करा';

  @override
  String get forgotPassword => 'पासवर्ड विसरलात?';

  @override
  String get forgotPasswordMessage =>
      'पासवर्ड विसरलात कार्यक्षमता अजून लागू केलेली नाही.';

  @override
  String get signUp => 'साइन अप करा';

  @override
  String get signUpSuccess => 'यशस्वीरित्या साइन अप झाले';

  @override
  String get weakPassword => 'दिलेला पासवर्ड खूप कमकुवत आहे.';

  @override
  String get emailAlreadyInUse => 'ईमेल आधीच वापरात आहे';

  @override
  String signUpFailed(Object errorMessage) {
    return 'साइन अप अयशस्वी झाले: $errorMessage';
  }

  @override
  String get createAccount => 'नवीन खाते तयार करा';

  @override
  String get pleaseEnterName => 'कृपया नाव टाका';

  @override
  String get alreadyHaveAccount => 'आधीच खाते आहे? लॉगिन करा';

  @override
  String get cancel => 'रद्द करा';

  @override
  String get sendResetLink => 'रीसेट लिंक पाठवा';

  @override
  String get passwordResetEmailSent =>
      'पासवर्ड रीसेट ईमेल पाठवला आहे. तुमचा इनबॉक्स तपासा.';

  @override
  String passwordResetFailed(Object errorMessage) {
    return 'पासवर्ड रीसेट अयशस्वी झाले: $errorMessage';
  }

  @override
  String get userNotFound => 'वापरकर्ता सापडला नाही';

  @override
  String get passwordResetSuccess => 'पासवर्ड यशस्वीरित्या रीसेट झाला';

  @override
  String get newPassword => 'नवीन पासवर्ड';

  @override
  String get pleaseEnterNewPassword => 'कृपया नवीन पासवर्ड टाका';

  @override
  String get resetPassword => 'पासवर्ड रीसेट करा';

  @override
  String get findUser => 'वापरकर्ता शोधा';

  @override
  String get pleaseEnterMandalName => 'कृपया मंडळाचे नाव टाका';

  @override
  String get pleaseEnterAddress => 'कृपया पत्ता टाका';

  @override
  String get pleaseEnterCurrentYear => 'कृपया चालू वर्ष टाका';

  @override
  String get profileSaved => 'प्रोफाइल सेव्ह झाले!';

  @override
  String get userName => 'वापरकर्त्याचे नाव';

  @override
  String get userEmail => 'वापरकर्त्याचा ईमेल';

  @override
  String get pleaseEnterReceiptNo => 'कृपया पावती क्रमांक टाका';

  @override
  String get pleaseEnterPrefix => 'कृपया उपपद टाका';

  @override
  String get pleaseEnterAmount => 'कृपया रक्कम टाका';

  @override
  String get pleaseEnterValidAmount => 'कृपया वैध रक्कम टाका';

  @override
  String get pleaseEnterValidNumber => 'कृपया वैध क्रमांक टाका';

  @override
  String get pleaseEnterRegistrationNo => 'कृपया नोंदणी क्रमांक टाका';

  @override
  String get pleaseEnterAmountInWords => 'कृपया अक्षरी रक्कम टाका';

  @override
  String get pdfGenerated => 'PDF तयार झाली';

  @override
  String get pdfGeneratedSuccessfully => 'PDF यशस्वीरित्या तयार झाली आहे.';

  @override
  String get ok => 'ओके';

  @override
  String get saveReceipt => 'पावती सेव्ह करा';

  @override
  String get clearForm => 'फॉर्म साफ करा';

  @override
  String get preview => 'पूर्वावलोकन';

  @override
  String get mobileNo => 'मोबाईल क्र.';

  @override
  String get generatedBy => 'यांच्याद्वारे तयार केले';

  @override
  String get pleaseEnterDonorName => 'कृपया देणगीदाराचे नाव टाका';

  @override
  String get noDonationsYet => 'अजून कोणतीही देणगी नाही.';

  @override
  String get pleaseEnterTitle => 'कृपया शीर्षक टाका';

  @override
  String get noExpensesYet => 'अजून कोणताही खर्च नाही.';

  @override
  String get downloadAllExpenses => 'सर्व खर्च डाउनलोड करा';

  @override
  String get shareAllExpenses => 'सर्व खर्च शेअर करा';

  @override
  String get netBalance => 'निव्वळ शिल्लक';

  @override
  String get selectLanguage => 'भाषा निवडा';

  @override
  String get shreeGaneshPrasanna => '|| श्री गणेश प्रसन्न ||';

  @override
  String get registrationNo => 'नोंदणी क्र.';

  @override
  String ganeshotsavYear(Object year) {
    return 'गणेशोत्सव $year';
  }

  @override
  String get from => 'यांच्याकडून';

  @override
  String get cashReceived => 'रोख मिळाले...!';

  @override
  String get thankYou => 'धन्यवाद ...!';

  @override
  String get signature => 'सही';

  @override
  String get logout => 'लॉगआउट';

  @override
  String get secretQuestion => 'गुप्त प्रश्न';

  @override
  String get pleaseEnterSecretQuestion => 'कृपया एक गुप्त प्रश्न टाका';

  @override
  String get secretAnswer => 'गुप्त उत्तर';

  @override
  String get pleaseEnterSecretAnswer => 'कृपया एक गुप्त उत्तर टाका';
}
