/// User entity representing a user in the domain layer
class UserEntity {
  final String? uid;
  final String name;
  final String email;
  final String password;
  final String secretQuestion;
  final String secretAnswer;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final bool isActive;
  
  const UserEntity({
    this.uid,
    required this.name,
    required this.email,
    required this.password,
    required this.secretQuestion,
    required this.secretAnswer,
    required this.createdAt,
    this.lastLoginAt,
    this.isActive = true,
  });
  
  /// Create a copy with updated fields
  UserEntity copyWith({
    String? uid,
    String? name,
    String? email,
    String? password,
    String? secretQuestion,
    String? secretAnswer,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isActive,
  }) {
    return UserEntity(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      password: password ?? this.password,
      secretQuestion: secretQuestion ?? this.secretQuestion,
      secretAnswer: secretAnswer ?? this.secretAnswer,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
    );
  }
  
  /// Convert to map for storage
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'password': password,
      'secretQuestion': secretQuestion,
      'secretAnswer': secretAnswer,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastLoginAt': lastLoginAt?.millisecondsSinceEpoch,
      'isActive': isActive,
    };
  }
  
  /// Create from map
  factory UserEntity.fromMap(Map<String, dynamic> map) {
    return UserEntity(
      uid: map['uid'],
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      password: map['password'] ?? '',
      secretQuestion: map['secretQuestion'] ?? '',
      secretAnswer: map['secretAnswer'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      lastLoginAt: map['lastLoginAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['lastLoginAt'])
          : null,
      isActive: map['isActive'] ?? true,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserEntity &&
        other.uid == uid &&
        other.name == name &&
        other.email == email &&
        other.password == password &&
        other.secretQuestion == secretQuestion &&
        other.secretAnswer == secretAnswer &&
        other.createdAt == createdAt &&
        other.lastLoginAt == lastLoginAt &&
        other.isActive == isActive;
  }
  
  @override
  int get hashCode {
    return uid.hashCode ^
        name.hashCode ^
        email.hashCode ^
        password.hashCode ^
        secretQuestion.hashCode ^
        secretAnswer.hashCode ^
        createdAt.hashCode ^
        lastLoginAt.hashCode ^
        isActive.hashCode;
  }
  
  @override
  String toString() {
    return 'UserEntity(uid: $uid, name: $name, email: $email, isActive: $isActive)';
  }
}
