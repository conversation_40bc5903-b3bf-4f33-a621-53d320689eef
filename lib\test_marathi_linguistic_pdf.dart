import 'package:flutter/material.dart';
import 'package:wargani/marathi_linguistic_pdf_generator.dart';

/// Test app for Marathi Linguistic PDF Generator
/// 
/// This app demonstrates the generation of a comprehensive PDF
/// explaining Marathi linguistic terms with proper Unicode support

class TestMarathiLinguisticApp extends StatelessWidget {
  const TestMarathiLinguisticApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'मराठी भाषाशास्त्रीय PDF',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        fontFamily: 'Roboto',
      ),
      home: const MarathiLinguisticScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// Main function to run the test app
void main() {
  runApp(const TestMarathiLinguisticApp());
}

/// Standalone function to generate PDF programmatically
/// 
/// Usage:
/// ```dart
/// final pdfPath = await generateMarathiLinguisticPdfStandalone();
/// print('PDF generated at: $pdfPath');
/// ```
Future<String?> generateMarathiLinguisticPdfStandalone() async {
  try {
    print('🔤 Starting standalone Marathi linguistic PDF generation...');
    
    final pdfPath = await MarathiLinguisticPdfGenerator.generateMarathiLinguisticPdf();
    
    if (pdfPath != null) {
      print('✅ PDF generated successfully at: $pdfPath');
    } else {
      print('✅ PDF opened in browser (Web platform)');
    }
    
    return pdfPath;
    
  } catch (e) {
    print('❌ Error generating PDF: $e');
    rethrow;
  }
}

/// Test function to verify conjunct character rendering
void testConjunctCharacters() {
  print('🧪 Testing conjunct character rendering...');
  
  final testCases = [
    'श्री गणेश प्रसन्न',
    'क्षत्रिय वर्ग',
    'त्रिकोण आकार',
    'ज्ञानेश्वर महाराज',
    'स्वातंत्र्य दिन',
    'प्रसन्न चित्त',
    'स्थापना दिवस',
    'न्यायालय व्यवस्था',
    'गृहकार्य',
    'विद्यालय',
    'सूर्यप्रकाश',
  ];
  
  print('📝 Test cases for conjunct characters:');
  for (int i = 0; i < testCases.length; i++) {
    print('${i + 1}. ${testCases[i]}');
  }
  
  print('✅ All test cases prepared. Generate PDF to verify rendering.');
}

/// Instructions for using this test:
/// 
/// 1. **Run the Flutter app:**
///    ```bash
///    flutter run lib/test_marathi_linguistic_pdf.dart
///    ```
/// 
/// 2. **Generate PDF programmatically:**
///    ```dart
///    final pdfPath = await generateMarathiLinguisticPdfStandalone();
///    ```
/// 
/// 3. **Test conjunct characters:**
///    ```dart
///    testConjunctCharacters();
///    ```
/// 
/// 4. **What to verify in the generated PDF:**
///    - ✅ All Marathi text renders correctly
///    - ✅ Conjunct characters (श्री, क्ष, त्र, ज्ञ) appear as single units
///    - ✅ No broken or missing characters
///    - ✅ Proper spacing and alignment
///    - ✅ Unicode Devanagari font is embedded
///    - ✅ PDF opens correctly in all viewers
/// 
/// 5. **Expected content in PDF:**
///    - Section 1: जोडशब्द (Compound Words) with 3 examples
///    - Section 2: वेलांटी (Virama/Halant) with explanation and examples
///    - Section 3: उकार (Ukar) with vowel modification examples
///    - Section 4: अवगड शब्द (Difficult Words) with 3 examples
///    - Proper formatting with bold headings and clean layout
/// 
/// 6. **Troubleshooting:**
///    - If conjuncts appear broken, check internet connectivity for Google Fonts
///    - If PDF doesn't generate, ensure proper permissions for file writing
///    - If fonts don't load, add Hind font to assets/fonts/ directory
/// 
/// 7. **Font fallback order:**
///    1. Noto Sans Devanagari (Primary - best for conjuncts)
///    2. Noto Serif Devanagari (Secondary)
///    3. Hind font from assets (Fallback)
/// 
/// 8. **Platform support:**
///    - ✅ Android: Saves PDF to device storage
///    - ✅ iOS: Saves PDF to device storage
///    - ✅ Web: Opens PDF in browser
///    - ✅ Desktop: Saves PDF to local directory

class MarathiLinguisticTestWidget extends StatefulWidget {
  const MarathiLinguisticTestWidget({super.key});

  @override
  State<MarathiLinguisticTestWidget> createState() => _MarathiLinguisticTestWidgetState();
}

class _MarathiLinguisticTestWidgetState extends State<MarathiLinguisticTestWidget> {
  String _testResults = 'Ready to run tests';

  void _runConjunctTests() {
    setState(() {
      _testResults = 'Running conjunct character tests...';
    });

    testConjunctCharacters();

    setState(() {
      _testResults = '✅ Conjunct character tests completed. Check console for details.';
    });
  }

  Future<void> _generateTestPdf() async {
    setState(() {
      _testResults = 'Generating test PDF...';
    });

    try {
      final pdfPath = await generateMarathiLinguisticPdfStandalone();
      setState(() {
        _testResults = pdfPath != null 
            ? '✅ Test PDF generated: $pdfPath'
            : '✅ Test PDF opened in browser';
      });
    } catch (e) {
      setState(() {
        _testResults = '❌ Test PDF generation failed: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marathi Linguistic PDF Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Marathi Linguistic PDF Generator Test',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Test Results:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(_testResults),
            ),
            
            const SizedBox(height: 30),
            
            // Test buttons
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _runConjunctTests,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.all(15),
                ),
                child: const Text(
                  'Test Conjunct Characters',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _generateTestPdf,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.all(15),
                ),
                child: const Text(
                  'Generate Test PDF',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Instructions
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            const Text(
              '1. Click "Test Conjunct Characters" to verify character processing\n'
              '2. Click "Generate Test PDF" to create the linguistic terms PDF\n'
              '3. Check the generated PDF for proper Marathi text rendering\n'
              '4. Verify that all conjunct characters appear correctly',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
