import 'package:flutter/material.dart';
import 'package:wargani/conjunct_test_receipt.dart';

/// Test app specifically for conjunct character testing
/// 
/// This app focuses on solving the conjunct character rendering issue
/// by using explicit Unicode sequences and proper font handling

class TestConjunctReceiptApp extends StatelessWidget {
  const TestConjunctReceiptApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Conjunct Character Test',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        fontFamily: 'Roboto',
      ),
      home: const ConjunctTestReceiptScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// Main function to run the conjunct test app
void main() {
  runApp(const TestConjunctReceiptApp());
}

/// Standalone function to generate test receipt
Future<String?> generateConjunctTestReceiptStandalone() async {
  try {
    print('🧾 Generating standalone conjunct test receipt...');
    
    final pdfPath = await ConjunctTestReceiptGenerator.generateConjunctTestReceipt(
      amount: 4425.0,
      donorName: 'श्री राम शर्मा',
      receiptNumber: 1001,
    );
    
    if (pdfPath != null) {
      print('✅ Conjunct test receipt generated: $pdfPath');
    } else {
      print('✅ Conjunct test receipt opened in browser');
    }
    
    return pdfPath;
    
  } catch (e) {
    print('❌ Error generating conjunct test receipt: $e');
    rethrow;
  }
}

/// Test function to verify Unicode sequences
void testUnicodeSequences() {
  print('🔤 Testing Unicode sequences for conjunct characters...');
  
  final testCases = [
    {
      'original': 'श्री',
      'unicode': '\u0936\u094D\u0930\u0940',
      'description': 'श + ् + र + ी'
    },
    {
      'original': 'प्रसन्न',
      'unicode': '\u092A\u094D\u0930\u0938\u0928\u094D\u0928',
      'description': 'प + ् + र + स + न + ् + न'
    },
    {
      'original': 'गणेश',
      'unicode': '\u0917\u0923\u0947\u0936',
      'description': 'ग + ण + े + श'
    },
    {
      'original': 'यांच्याकडून',
      'unicode': '\u092F\u093E\u0902\u091A\u094D\u092F\u093E\u0915\u0921\u0942\u0928',
      'description': 'Complex conjunct sequence'
    },
    {
      'original': 'धन्यवाद',
      'unicode': '\u0927\u0928\u094D\u092F\u0935\u093E\u0926',
      'description': 'ध + न + ् + य + व + ा + द'
    },
    {
      'original': 'मिळाले',
      'unicode': '\u092E\u093F\u0933\u093E\u0932\u0947',
      'description': 'म + ि + ळ + ा + ल + े'
    },
  ];
  
  print('📝 Unicode sequence test cases:');
  for (int i = 0; i < testCases.length; i++) {
    final testCase = testCases[i];
    print('${i + 1}. ${testCase['original']} → ${testCase['unicode']} (${testCase['description']})');
  }
  
  print('✅ Unicode sequence tests prepared.');
}

/// Test widget for comprehensive conjunct testing
class ComprehensiveConjunctTestWidget extends StatefulWidget {
  const ComprehensiveConjunctTestWidget({super.key});

  @override
  State<ComprehensiveConjunctTestWidget> createState() => _ComprehensiveConjunctTestWidgetState();
}

class _ComprehensiveConjunctTestWidgetState extends State<ComprehensiveConjunctTestWidget> {
  String _testResults = 'Ready to run comprehensive conjunct tests';
  bool _isGenerating = false;

  void _runUnicodeTests() {
    setState(() {
      _testResults = 'Running Unicode sequence tests...';
    });

    testUnicodeSequences();

    setState(() {
      _testResults = '✅ Unicode sequence tests completed. Check console for details.';
    });
  }

  Future<void> _generateTestReceipt() async {
    setState(() {
      _testResults = 'Generating conjunct test receipt...';
      _isGenerating = true;
    });

    try {
      final pdfPath = await generateConjunctTestReceiptStandalone();
      setState(() {
        _testResults = pdfPath != null 
            ? '✅ Conjunct test receipt generated: $pdfPath'
            : '✅ Conjunct test receipt opened in browser';
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _testResults = '❌ Conjunct test receipt generation failed: $e';
        _isGenerating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Comprehensive Conjunct Test'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Comprehensive Conjunct Character Test',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Test Results:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(_testResults),
            ),
            
            const SizedBox(height: 30),
            
            // Test buttons
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _runUnicodeTests,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.all(15),
                ),
                child: const Text(
                  'Test Unicode Sequences',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isGenerating ? null : _generateTestReceipt,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.all(15),
                ),
                child: _isGenerating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(color: Colors.white),
                          ),
                          SizedBox(width: 10),
                          Text('Generating...', style: TextStyle(color: Colors.white)),
                        ],
                      )
                    : const Text(
                        'Generate Conjunct Test Receipt',
                        style: TextStyle(color: Colors.white),
                      ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Critical conjuncts display
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🔍 Critical Conjuncts to Test:',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                  SizedBox(height: 10),
                  Text(
                    '1. श्री गणेश प्रसन्न (Title)\n'
                    '2. यांच्याकडून (From suffix)\n'
                    '3. धन्यवाद (Thank you)\n'
                    '4. मिळाले (Received)\n'
                    '5. क्ष त्र ज्ञ प्र स्व स्थ द्व (Common conjuncts)\n'
                    '6. न्त न्द स्त न्य त्य द्य (More conjuncts)',
                    style: TextStyle(fontSize: 14, color: Colors.orange),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Instructions
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            const Text(
              '1. Run Unicode sequence tests to verify character processing\n'
              '2. Generate test receipt to verify PDF rendering\n'
              '3. Check generated PDF for proper conjunct rendering\n'
              '4. Verify all critical conjuncts appear as single characters\n'
              '5. Ensure no broken or separated character parts',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

/// Instructions for using this conjunct test:
/// 
/// 1. **Run the test app:**
///    ```bash
///    flutter run lib/test_conjunct_receipt.dart
///    ```
/// 
/// 2. **What this test solves:**
///    - Uses explicit Unicode codepoints for conjunct characters
///    - Tests multiple font fallbacks
///    - Focuses specifically on problematic conjuncts
///    - Provides detailed logging for debugging
/// 
/// 3. **Critical conjuncts tested:**
///    - श्री (Shri) - Most common and problematic
///    - प्रसन्न (Prasanna) - Complex conjunct sequence
///    - यांच्याकडून (From them) - Multiple conjuncts
///    - धन्यवाद (Thank you) - न्य conjunct
///    - मिळाले (Received) - ळ character
/// 
/// 4. **Expected results:**
///    - All conjuncts should appear as single characters
///    - No broken or separated parts
///    - Proper spacing and alignment
///    - Clear, readable Marathi text
/// 
/// 5. **If conjuncts still break:**
///    - Check internet connectivity for Google Fonts
///    - Add Hind-Regular.ttf to assets/fonts/
///    - Verify PDF viewer supports Unicode fonts
///    - Try different PDF viewers for comparison
/// 
/// 6. **Font loading order:**
///    1. Noto Sans Devanagari (Primary)
///    2. Noto Serif Devanagari (Secondary)
///    3. Hind Regular (Tertiary)
///    4. Kalam Regular (Quaternary)
///    5. Hind from assets (Fallback)
