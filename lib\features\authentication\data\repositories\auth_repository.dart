import 'package:wargani/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:wargani/features/authentication/domain/entities/user_entity.dart';
import 'package:wargani/features/authentication/domain/repositories/auth_repository_interface.dart';
import 'package:wargani/core/utils/result.dart';

/// Implementation of authentication repository
class AuthRepository implements AuthRepositoryInterface {
  final AuthLocalDataSource _localDataSource;
  
  AuthRepository(this._localDataSource);
  
  @override
  Future<Result<UserEntity>> login(String email, String password) async {
    try {
      final user = await _localDataSource.login(email, password);
      if (user != null) {
        return Result.success(user);
      } else {
        return Result.failure('Invalid email or password');
      }
    } catch (e) {
      return Result.failure('Login failed: ${e.toString()}');
    }
  }
  
  @override
  Future<Result<UserEntity>> register(UserEntity user) async {
    try {
      // Check if user already exists
      final existingUser = await _localDataSource.getUserByEmail(user.email);
      if (existingUser != null) {
        return Result.failure('User with this email already exists');
      }
      
      final savedUser = await _localDataSource.saveUser(user);
      return Result.success(savedUser);
    } catch (e) {
      return Result.failure('Registration failed: ${e.toString()}');
    }
  }
  
  @override
  Future<Result<void>> logout() async {
    try {
      await _localDataSource.clearCurrentUser();
      return Result.success(null);
    } catch (e) {
      return Result.failure('Logout failed: ${e.toString()}');
    }
  }
  
  @override
  Future<Result<UserEntity?>> getCurrentUser() async {
    try {
      final user = await _localDataSource.getCurrentUser();
      return Result.success(user);
    } catch (e) {
      return Result.failure('Failed to get current user: ${e.toString()}');
    }
  }
  
  @override
  Future<Result<void>> resetPassword(String email, String newPassword, String secretAnswer) async {
    try {
      final user = await _localDataSource.getUserByEmail(email);
      if (user == null) {
        return Result.failure('User not found');
      }
      
      if (user.secretAnswer.toLowerCase() != secretAnswer.toLowerCase()) {
        return Result.failure('Incorrect secret answer');
      }
      
      final updatedUser = user.copyWith(password: newPassword);
      await _localDataSource.updateUser(updatedUser);
      return Result.success(null);
    } catch (e) {
      return Result.failure('Password reset failed: ${e.toString()}');
    }
  }
  
  @override
  Future<Result<bool>> isUserLoggedIn() async {
    try {
      final user = await _localDataSource.getCurrentUser();
      return Result.success(user != null);
    } catch (e) {
      return Result.failure('Failed to check login status: ${e.toString()}');
    }
  }
}
