import 'package:flutter/material.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/utils/hive_helper.dart';

/// Test script to verify conjunct character rendering in PDF
/// 
/// This script tests various Marathi/Hindi conjunct characters
/// to ensure they render properly in generated PDFs

void main() async {
  print('🧪 Testing Conjunct Character Rendering in PDF...\n');
  
  // Initialize Hive
  await HiveHelper.init();
  
  // Test conjunct characters
  await testConjunctCharacters();
  
  print('\n✅ Conjunct character testing completed!');
  print('📄 Check generated PDF files to verify rendering.');
}

Future<void> testConjunctCharacters() async {
  // Create test profile with conjunct characters
  final profile = Profile(
    mandalName: 'श्री गणेश मंडळ', // Contains श्री conjunct
    address: 'पुणे, महाराष्ट्र',
    currentYear: '2024',
    mandalRegistrationNo: 'MH/2024/001',
  );
  
  // Save profile
  final profileBox = HiveHelper.getProfileBox();
  await profileBox.clear(); // Clear existing
  await profileBox.add(profile);
  
  // Test cases with various conjunct characters
  final testCases = [
    {
      'name': 'श्री राम शर्मा',        // श्री conjunct
      'description': 'Basic श्री conjunct test'
    },
    {
      'name': 'क्षत्रिय वर्ग',        // क्ष conjunct  
      'description': 'क्ष conjunct test'
    },
    {
      'name': 'त्रिकोण आकार',        // त्र conjunct
      'description': 'त्र conjunct test'
    },
    {
      'name': 'ज्ञानेश्वर महाराज',     // ज्ञ conjunct
      'description': 'ज्ञ conjunct test'
    },
    {
      'name': 'द्विवेदी कुटुंब',       // द्व conjunct
      'description': 'द्व conjunct test'
    },
    {
      'name': 'स्वतंत्रता दिन',       // स्व + त्र conjuncts
      'description': 'Multiple conjuncts test'
    },
    {
      'name': 'प्रसन्न चित्त',        // प्र conjunct
      'description': 'प्र conjunct test'
    },
    {
      'name': 'स्थापना दिवस',        // स्थ conjunct
      'description': 'स्थ conjunct test'
    },
    {
      'name': 'न्यायालय व्यवस्था',     // न्य conjunct
      'description': 'न्य conjunct test'
    },
    {
      'name': 'सत्त्व गुण',          // त्त्व conjunct
      'description': 'Complex त्त्व conjunct test'
    }
  ];
  
  print('📝 Testing ${testCases.length} conjunct character cases...\n');
  
  for (int i = 0; i < testCases.length; i++) {
    final testCase = testCases[i];
    
    print('${i + 1}. Testing: ${testCase['name']}');
    print('   Description: ${testCase['description']}');
    
    // Create Wargani receipt with conjunct characters
    final wargani = Wargani(
      receiptNo: i + 1,
      name: testCase['name']!,
      amount: (i + 1) * 1000.0,
      date: DateTime.now(),
      prefix: 'WR',
      mobileNo: '9876543210',
      amountInWords: 'रुपये ${(i + 1) * 1000}',
    );
    
    try {
      // Test PDF generation (Note: This requires a BuildContext)
      // In actual app, you would call:
      // final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(context, wargani, 'Test User');
      
      print('   ✅ Test case prepared successfully');
      
      // Print the characters for manual verification
      print('   📋 Characters to verify in PDF:');
      final chars = testCase['name']!.split('');
      for (final char in chars) {
        if (char.contains('्')) {
          print('      🔍 Conjunct: $char');
        }
      }
      
    } catch (e) {
      print('   ❌ Error: $e');
    }
    
    print('');
  }
}

/// Manual testing instructions:
/// 
/// 1. Run this in your Flutter app with proper BuildContext
/// 2. Generate PDFs for each test case
/// 3. Open PDFs and verify conjunct characters
/// 4. Look for these specific issues:
///    - Broken conjunct characters (appearing as separate letters)
///    - Missing halant (्) characters
///    - Incorrect spacing between characters
///    - Font fallback to non-Devanagari fonts
/// 
/// Expected results:
/// ✅ श्री should appear as single conjunct, not श + ् + री
/// ✅ क्ष should appear as single character, not क + ् + ष  
/// ✅ त्र should appear as single character, not त + ् + र
/// ✅ All matras should be properly positioned
/// ✅ Text should be properly aligned and spaced
/// 
/// If issues persist:
/// 1. Check internet connectivity for Google Fonts
/// 2. Verify Hind font is in assets/fonts/
/// 3. Test on different devices
/// 4. Check PDF viewer compatibility

class ConjunctTestWidget extends StatelessWidget {
  const ConjunctTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Conjunct Character Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Conjunct Characters:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Display test characters
            const Text('श्री गणेश प्रसन्न', style: TextStyle(fontSize: 24)),
            const Text('क्षत्रिय वर्ग', style: TextStyle(fontSize: 24)),
            const Text('त्रिकोण आकार', style: TextStyle(fontSize: 24)),
            const Text('ज्ञानेश्वर महाराज', style: TextStyle(fontSize: 24)),
            const Text('द्विवेदी कुटुंब', style: TextStyle(fontSize: 24)),
            const Text('स्वतंत्रता दिन', style: TextStyle(fontSize: 24)),
            const Text('प्रसन्न चित्त', style: TextStyle(fontSize: 24)),
            const Text('स्थापना दिवस', style: TextStyle(fontSize: 24)),
            const Text('न्यायालय व्यवस्था', style: TextStyle(fontSize: 24)),
            const Text('सत्त्व गुण', style: TextStyle(fontSize: 24)),
            
            const SizedBox(height: 32),
            
            ElevatedButton(
              onPressed: () async {
                // Test PDF generation with conjunct characters
                await _testPdfGeneration(context);
              },
              child: const Text('Generate Test PDF'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testPdfGeneration(BuildContext context) async {
    try {
      // Initialize if needed
      await HiveHelper.init();
      
      // Create test data
      final profile = Profile(
        mandalName: 'श्री गणेश मंडळ',
        address: 'पुणे, महाराष्ट्र', 
        currentYear: '2024',
        mandalRegistrationNo: 'MH/2024/001',
      );
      
      final profileBox = HiveHelper.getProfileBox();
      await profileBox.clear();
      await profileBox.add(profile);
      
      final wargani = Wargani(
        receiptNo: 1,
        name: 'श्री क्षत्रिय त्रिकोण',
        amount: 1100.0,
        date: DateTime.now(),
        prefix: 'WR',
        mobileNo: '9876543210',
        amountInWords: 'एक हजार एकशे रुपये',
      );
      
      // Generate PDF
      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        wargani,
        'Test User',
      );
      
      if (pdfPath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('PDF generated: $pdfPath')),
        );
      }
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }
}
