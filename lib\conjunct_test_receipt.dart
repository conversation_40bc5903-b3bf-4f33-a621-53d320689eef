import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';

/// Conjunct Character Test Receipt Generator
/// 
/// This is a focused test to solve the conjunct character rendering issue
/// Uses explicit Unicode sequences and multiple font fallbacks

class ConjunctTestReceiptGenerator {
  
  // CRITICAL: Use explicit Unicode codepoints for conjunct characters
  static String _fixConjunctCharacters(String text) {
    // Map of problematic conjuncts to their explicit Unicode sequences
    final Map<String, String> unicodeFixes = {
      // Most critical conjuncts with explicit Unicode codepoints
      'श्री': '\u0936\u094D\u0930\u0940',  // श + ् + र + ी
      'प्रसन्न': '\u092A\u094D\u0930\u0938\u0928\u094D\u0928', // प + ् + र + स + न + ् + न
      'गणेश': '\u0917\u0923\u0947\u0936', // ग + ण + े + श
      'यांच्याकडून': '\u092F\u093E\u0902\u091A\u094D\u092F\u093E\u0915\u0921\u0942\u0928',
      'धन्यवाद': '\u0927\u0928\u094D\u092F\u0935\u093E\u0926',
      'मिळाले': '\u092E\u093F\u0933\u093E\u0932\u0947',
      'क्ष': '\u0915\u094D\u0937',        // क + ् + ष
      'त्र': '\u0924\u094D\u0930',        // त + ् + र
      'ज्ञ': '\u091C\u094D\u091E',        // ज + ् + ञ
      'प्र': '\u092A\u094D\u0930',        // प + ् + र
      'स्व': '\u0938\u094D\u0935',        // स + ् + व
      'स्थ': '\u0938\u094D\u0925',        // स + ् + थ
      'द्व': '\u0926\u094D\u0935',        // द + ् + व
      'न्त': '\u0928\u094D\u0924',        // न + ् + त
      'न्द': '\u0928\u094D\u0926',        // न + ् + द
      'स्त': '\u0938\u094D\u0924',        // स + ् + त
      'न्य': '\u0928\u094D\u092F',        // न + ् + य
      'त्य': '\u0924\u094D\u092F',        // त + ् + य
      'द्य': '\u0926\u094D\u092F',        // द + ् + य
      'म्प': '\u092E\u094D\u092A',        // म + ् + प
      'म्ब': '\u092E\u094D\u092C',        // म + ् + ब
      'च्च': '\u091A\u094D\u091A',        // च + ् + च
      'त्त': '\u0924\u094D\u0924',        // त + ् + त
      'ल्ल': '\u0932\u094D\u0932',        // ल + ् + ल
      'न्न': '\u0928\u094D\u0928',        // न + ् + न
    };
    
    String result = text;
    unicodeFixes.forEach((broken, fixed) {
      result = result.replaceAll(broken, fixed);
    });
    
    return result;
  }

  // Get best available Devanagari font
  static Future<pw.Font> _getBestDevanagariFont() async {
    final fontAttempts = [
      () async => await PdfGoogleFonts.notoSansDevanagariRegular(),
      () async => await PdfGoogleFonts.notoSerifDevanagariRegular(),
      () async => await PdfGoogleFonts.hindRegular(),
      () async => await PdfGoogleFonts.kalamRegular(),
      () async {
        final fontData = await rootBundle.load("assets/fonts/Hind-Regular.ttf");
        return pw.Font.ttf(fontData);
      },
    ];

    for (int i = 0; i < fontAttempts.length; i++) {
      try {
        final font = await fontAttempts[i]();
        print('✅ Loaded font (attempt ${i + 1})');
        return font;
      } catch (e) {
        print('⚠️ Font attempt ${i + 1} failed: $e');
        if (i == fontAttempts.length - 1) {
          throw Exception('❌ No Devanagari font available');
        }
      }
    }
    throw Exception('❌ Unexpected font loading error');
  }

  static Future<pw.Font> _getBoldFont() async {
    try {
      return await PdfGoogleFonts.notoSansDevanagariBold();
    } catch (e) {
      return await _getBestDevanagariFont();
    }
  }

  // Create text widget with proper Unicode handling
  static pw.Widget _createText(String text, pw.Font font, double size, {bool bold = false}) {
    final processedText = _fixConjunctCharacters(text);
    
    return pw.Text(
      processedText,
      style: pw.TextStyle(
        font: font,
        fontSize: size,
        fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
        letterSpacing: 0.0, // Critical: No letter spacing for conjuncts
        height: 1.4,
      ),
    );
  }

  /// Generate test receipt with focus on conjunct character rendering
  static Future<String?> generateConjunctTestReceipt({
    required double amount,
    required String donorName,
    required int receiptNumber,
  }) async {
    try {
      print('🧾 Generating conjunct test receipt...');
      
      final font = await _getBestDevanagariFont();
      final boldFont = await _getBoldFont();
      
      final pdf = pw.Document();
      
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(30),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with critical conjuncts
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex("#FF8C00"),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      // CRITICAL TEST: श्री गणेश प्रसन्न
                      _createText('|| श्री गणेश प्रसन्न ||', boldFont, 20, bold: true),
                      pw.SizedBox(height: 8),
                      _createText('गणेशोत्सव 2025', font, 14),
                    ],
                  ),
                ),
                
                pw.SizedBox(height: 30),
                
                // Receipt details
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    _createText('पावती नं. $receiptNumber', boldFont, 16, bold: true),
                    _createText('दिनांक: ${DateFormat('dd/MM/yyyy').format(DateTime.now())}', font, 14),
                  ],
                ),
                
                pw.SizedBox(height: 25),
                
                // Donor name with conjunct test
                pw.Row(
                  children: [
                    _createText('सो.श्री ', font, 14),
                    pw.Expanded(
                      child: pw.Container(
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(color: PdfColors.black),
                          ),
                        ),
                        child: pw.Text(
                          donorName,
                          style: pw.TextStyle(font: boldFont, fontSize: 14, fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                    ),
                    // CRITICAL TEST: यांच्याकडून
                    _createText(' यांच्याकडून', font, 14),
                  ],
                ),
                
                pw.SizedBox(height: 25),
                
                // Amount
                pw.Row(
                  children: [
                    _createText('रोख मिळाले: ', font, 14),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.black),
                        borderRadius: pw.BorderRadius.circular(5),
                      ),
                      child: pw.Text(
                        '₹ ${amount.toStringAsFixed(0)}',
                        style: pw.TextStyle(font: boldFont, fontSize: 16, fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // Amount in words
                pw.Row(
                  children: [
                    _createText('अक्षरी: ', font, 12),
                    pw.Expanded(
                      child: pw.Container(
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(color: PdfColors.black),
                          ),
                        ),
                        child: _createText('चार हजार चारशे पंचवीस रुपये फक्त', font, 12),
                      ),
                    ),
                  ],
                ),
                
                pw.Spacer(),
                
                // Footer with critical conjuncts
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        // CRITICAL TEST: धन्यवाद
                        _createText('धन्यवाद...!', boldFont, 14, bold: true),
                        pw.SizedBox(height: 15),
                        _createText('सही', font, 12),
                      ],
                    ),
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        // CRITICAL TEST: मिळाले
                        _createText('रोख मिळाले...!', boldFont, 14, bold: true),
                      ],
                    ),
                  ],
                ),
                
                pw.SizedBox(height: 20),
                
                // Conjunct test section
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex("#F0F8FF"),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      _createText('Conjunct Character Test:', font, 10),
                      pw.SizedBox(height: 5),
                      _createText('श्री, प्रसन्न, गणेश, यांच्याकडून, धन्यवाद, मिळाले', font, 10),
                      pw.SizedBox(height: 5),
                      _createText('क्ष, त्र, ज्ञ, प्र, स्व, स्थ, द्व, न्त, न्द, स्त', font, 10),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'Developed by AMSSoftX Web: https://amsssoftx.com',
                        style: pw.TextStyle(font: font, fontSize: 8),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      );
      
      // Save PDF
      if (kIsWeb) {
        await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
        return null;
      } else {
        final output = await getTemporaryDirectory();
        final file = File("${output.path}/conjunct_test_receipt_${receiptNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf");
        await file.writeAsBytes(await pdf.save());
        print('✅ Conjunct test receipt generated: ${file.path}');
        return file.path;
      }
      
    } catch (e) {
      print('❌ Error generating conjunct test receipt: $e');
      throw Exception('Failed to generate conjunct test receipt: $e');
    }
  }
}

/// Flutter Widget for Conjunct Test Receipt
class ConjunctTestReceiptScreen extends StatefulWidget {
  const ConjunctTestReceiptScreen({super.key});

  @override
  State<ConjunctTestReceiptScreen> createState() => _ConjunctTestReceiptScreenState();
}

class _ConjunctTestReceiptScreenState extends State<ConjunctTestReceiptScreen> {
  final _amountController = TextEditingController(text: '4425');
  final _donorController = TextEditingController(text: 'राम शर्मा');
  final _receiptController = TextEditingController(text: '1001');
  
  bool _isGenerating = false;
  String _status = 'Ready to test conjunct characters';

  Future<void> _generateTestReceipt() async {
    setState(() {
      _isGenerating = true;
      _status = 'Generating conjunct test receipt...';
    });

    try {
      final pdfPath = await ConjunctTestReceiptGenerator.generateConjunctTestReceipt(
        amount: double.parse(_amountController.text),
        donorName: _donorController.text,
        receiptNumber: int.parse(_receiptController.text),
      );

      setState(() {
        _status = pdfPath != null 
            ? '✅ Test receipt generated: $pdfPath'
            : '✅ Test receipt opened in browser';
        _isGenerating = false;
      });

    } catch (e) {
      setState(() {
        _status = '❌ Error: $e';
        _isGenerating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Conjunct Character Test'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'Conjunct Character Test Receipt',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 20),
            
            TextField(
              controller: _amountController,
              decoration: const InputDecoration(labelText: 'Amount'),
              keyboardType: TextInputType.number,
            ),
            
            const SizedBox(height: 10),
            
            TextField(
              controller: _donorController,
              decoration: const InputDecoration(labelText: 'Donor Name'),
            ),
            
            const SizedBox(height: 10),
            
            TextField(
              controller: _receiptController,
              decoration: const InputDecoration(labelText: 'Receipt Number'),
              keyboardType: TextInputType.number,
            ),
            
            const SizedBox(height: 20),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(_status),
            ),
            
            const SizedBox(height: 20),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isGenerating ? null : _generateTestReceipt,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: _isGenerating
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text('Generate Conjunct Test Receipt', style: TextStyle(color: Colors.white)),
              ),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Critical Conjuncts to Test:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const Text(
              'श्री गणेश प्रसन्न\n'
              'यांच्याकडून\n'
              'धन्यवाद\n'
              'मिळाले\n'
              'क्ष त्र ज्ञ प्र स्व स्थ द्व न्त न्द स्त',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
