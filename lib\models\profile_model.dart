import 'package:hive/hive.dart';
import 'dart:typed_data'; // Import Uint8List

part 'profile_model.g.dart';

@HiveType(typeId: 0)
class Profile extends HiveObject {
  @HiveField(0)
  String mandalName;

  @HiveField(1)
  String address;

  @HiveField(2)
  Uint8List? logoBytes; // Change from logoPath to logoBytes

  @HiveField(3)
  String currentYear;

  @HiveField(4)
  String? mandalRegistrationNo;

  @HiveField(5)
  String? festivalName;

  @HiveField(6)
  Uint8List? leftLogoBytes;

  @HiveField(7)
  Uint8List? rightLogoBytes;

  @HiveField(8)
  String? customHeaderText;

  Profile({
    required this.mandalName,
    required this.address,
    this.logoBytes, // Update constructor parameter
    required this.currentYear,
    this.mandalRegistrationNo,
    this.festivalName,
    this.leftLogoBytes,
    this.rightLogoBytes,
    this.customHeaderText,
  });
}
