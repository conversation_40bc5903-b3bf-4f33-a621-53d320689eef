import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/screens/profile_screen.dart';
import 'package:wargani/features/receipts/presentation/screens/enhanced_wargani_receipt_screen.dart';
import 'package:wargani/features/donations/presentation/screens/enhanced_donations_screen.dart';
import 'package:wargani/features/expenses/presentation/screens/enhanced_expenses_screen.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/widgets/dashboard_card.dart';
import 'package:wargani/widgets/footer.dart';

/// Enhanced professional dashboard with animations and better UX
class EnhancedDashboardScreen extends StatefulWidget {
  const EnhancedDashboardScreen({super.key});

  @override
  State<EnhancedDashboardScreen> createState() => _EnhancedDashboardScreenState();
}

class _EnhancedDashboardScreenState extends State<EnhancedDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: _buildAppBar(localizations),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(localizations),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AppLocalizations localizations) {
    return AppBar(
      title: Text(localizations.dashboard),
      elevation: 0,
      backgroundColor: context.colorScheme.surface,
      foregroundColor: context.colorScheme.onSurface,
      actions: [
        IconButton(
          icon: const Icon(Icons.person),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ProfileScreen(),
              ),
            ).then((_) => setState(() {}));
          },
        ),
      ],
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    return Column(
      children: [
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeSection(localizations),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildStatsGrid(localizations),

                ],
              ),
            ),
          ),
        ),
        const Footer(),
      ],
    );
  }

  Widget _buildWelcomeSection(AppLocalizations localizations) {
    final userBox = HiveHelper.getUsersBox();
    final profileBox = HiveHelper.getProfileBox();
    final user = userBox.isNotEmpty ? userBox.getAt(0) : null;
    final profile = profileBox.isNotEmpty ? profileBox.getAt(0) : null;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.colorScheme.primary,
            context.colorScheme.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Row(
        children: [
          // Profile Logo
          if (profile?.logoBytes != null) ...[
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(28),
                child: Image.memory(
                  profile!.logoBytes!,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],

          // Welcome Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (profile != null && user != null) ...[
                  Text(
                    'Welcome back!',
                    style: context.textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.name.titleCase,
                    style: context.textTheme.titleMedium?.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    profile.mandalName,
                    style: context.textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.9),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${profile.festivalName ?? 'गणेशोत्सव'} ${profile.currentYear}',
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                ] else ...[
                  Text(
                    'Welcome to AMSSoftX Wargani App',
                    style: context.textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please complete your profile to get started',
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildStatsGrid(AppLocalizations localizations) {
    final warganiBox = HiveHelper.getWarganiBox();
    final donationsBox = HiveHelper.getDonationsBox();
    final expensesBox = HiveHelper.getExpensesBox();

    final totalPeople = warganiBox.length;
    final totalWargani = warganiBox.values
        .fold<double>(0, (sum, item) => sum + item.amount);
    final totalDonations = donationsBox.values
        .fold<double>(0, (sum, item) => sum + item.amount);
    final totalExpenses = expensesBox.values
        .fold<double>(0, (sum, item) => sum + item.amount);

    final stats = [
      _StatItem(
        title: localizations.warganiSummary,
        subtitle: 'Total People: $totalPeople\nTotal Amount: ${totalWargani.toCurrencyCompact}',
        icon: Icons.people,
        color: Colors.blue,
        onTap: () => _navigateToScreen(const EnhancedWarganiReceiptScreen()),
      ),
      _StatItem(
        title: localizations.donationSummary,
        subtitle: 'Total Donors: ${donationsBox.length}\nTotal Amount: ${totalDonations.toCurrencyCompact}',
        icon: Icons.volunteer_activism,
        color: Colors.green,
        onTap: () => _navigateToScreen(const EnhancedDonationsScreen()),
      ),
      _StatItem(
        title: localizations.expensesSummary,
        subtitle: 'Total Items: ${expensesBox.length}\nTotal Amount: ${totalExpenses.toCurrencyCompact}',
        icon: Icons.receipt_long,
        color: Colors.orange,
        onTap: () => _navigateToScreen(const EnhancedExpensesScreen()),
      ),
      _StatItem(
        title: localizations.netBalance,
        subtitle: '${(totalWargani + totalDonations - totalExpenses).toCurrencyCompact}',
        icon: Icons.account_balance_wallet,
        color: Colors.purple,
        onTap: () {},
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: context.isMobile ? 2 : 4,
        crossAxisSpacing: AppConstants.defaultPadding,
        mainAxisSpacing: AppConstants.defaultPadding,
        childAspectRatio: 1.1,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return _buildStatCard(stat, index);
      },
    );
  }

  Widget _buildStatCard(_StatItem stat, int index) {
    return Card(
      elevation: AppConstants.cardElevation,
      child: InkWell(
        onTap: stat.onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            gradient: LinearGradient(
              colors: [
                stat.color.withOpacity(0.1),
                stat.color.withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: stat.color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      stat.icon,
                      color: stat.color,
                      size: 24,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: context.colorScheme.onSurface.withOpacity(0.5),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                stat.title,
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Expanded(
                child: Text(
                  stat.subtitle,
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colorScheme.onSurface.withOpacity(0.7),
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideY(begin: 0.3, end: 0);
  }

  Widget _buildQuickActions(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: context.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),

      ],
    );
  }



  void _navigateToScreen(Widget screen) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    ).then((_) => setState(() {}));
  }

  Future<void> _refreshData() async {
    await Future.delayed(const Duration(milliseconds: 500));
    setState(() {});
  }
}

class _StatItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  _StatItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
