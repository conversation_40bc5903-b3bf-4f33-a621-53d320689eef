/// A generic result class for handling success and failure states
class Result<T> {
  final T? data;
  final String? error;
  final bool isSuccess;
  
  const Result._({
    this.data,
    this.error,
    required this.isSuccess,
  });
  
  /// Create a successful result
  factory Result.success(T data) {
    return Result._(
      data: data,
      isSuccess: true,
    );
  }
  
  /// Create a failure result
  factory Result.failure(String error) {
    return Result._(
      error: error,
      isSuccess: false,
    );
  }
  
  /// Check if result is failure
  bool get isFailure => !isSuccess;
  
  /// Get data or throw exception if failure
  T get dataOrThrow {
    if (isFailure) {
      throw Exception(error);
    }
    return data as T;
  }
  
  /// Get data or return default value if failure
  T dataOrDefault(T defaultValue) {
    return isSuccess ? data as T : defaultValue;
  }
  
  /// Transform the data if success
  Result<R> map<R>(R Function(T data) transform) {
    if (isSuccess) {
      try {
        return Result.success(transform(data as T));
      } catch (e) {
        return Result.failure(e.toString());
      }
    }
    return Result.failure(error!);
  }
  
  /// Execute function if success
  Result<T> onSuccess(void Function(T data) action) {
    if (isSuccess) {
      action(data as T);
    }
    return this;
  }
  
  /// Execute function if failure
  Result<T> onFailure(void Function(String error) action) {
    if (isFailure) {
      action(error!);
    }
    return this;
  }
}
