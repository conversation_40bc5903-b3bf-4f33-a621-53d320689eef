import 'package:hive/hive.dart';

part 'user_model.g.dart';

@HiveType(typeId: 4)
class User extends HiveObject {
  @HiveField(0)
  String? uid; // Make UID nullable

  @HiveField(1)
  String name;

  @HiveField(2)
  String email;

  @HiveField(3) // Update HiveField index
  String password;

  @HiveField(4)
  String secretQuestion;

  @HiveField(5)
  String secretAnswer;

  @HiveField(6)
  String? username;

  User({
    required this.uid,
    required this.name,
    required this.email,
    required this.password,
    required this.secretQuestion,
    required this.secretAnswer,
    this.username,
  });
}
