import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/l10n/app_localizations.dart';

/// Common layout widget that provides consistent bottom navigation across all screens
class CommonLayout extends StatelessWidget {
  final Widget child;
  final int currentIndex;
  final String? title;
  final List<Widget>? actions;
  final bool showBottomNav;
  final FloatingActionButton? floatingActionButton;

  const CommonLayout({
    super.key,
    required this.child,
    required this.currentIndex,
    this.title,
    this.actions,
    this.showBottomNav = true,
    this.floatingActionButton,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: title != null
          ? AppBar(
              title: Text(title!),
              actions: actions,
              backgroundColor: context.colorScheme.surface,
              elevation: 0,
            )
          : null,
      body: child,
      bottomNavigationBar: showBottomNav ? _buildBottomNavigationBar(context, localizations) : null,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildBottomNavigationBar(BuildContext context, AppLocalizations localizations) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: BottomNavigationBar(
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.dashboard_rounded),
            activeIcon: const Icon(Icons.dashboard_rounded),
            label: localizations.dashboard,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.receipt_long_rounded),
            activeIcon: const Icon(Icons.receipt_long_rounded),
            label: localizations.warganiReceipt,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.volunteer_activism_rounded),
            activeIcon: const Icon(Icons.volunteer_activism_rounded),
            label: localizations.donations,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.money_off_rounded),
            activeIcon: const Icon(Icons.money_off_rounded),
            label: localizations.expenses,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person_rounded),
            activeIcon: const Icon(Icons.person_rounded),
            label: localizations.profile,
          ),
        ],
        currentIndex: currentIndex,
        type: BottomNavigationBarType.fixed,
        onTap: (index) => _onItemTapped(context, index),
        elevation: 0,
        selectedFontSize: 12,
        unselectedFontSize: 10,
      ),
    ).animate().slideY(begin: 1, end: 0, duration: 600.ms, curve: Curves.easeOut);
  }

  void _onItemTapped(BuildContext context, int index) {
    if (index == currentIndex) return; // Don't navigate to same screen

    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/dashboard');
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/wargani');
        break;
      case 2:
        Navigator.pushReplacementNamed(context, '/donations');
        break;
      case 3:
        Navigator.pushReplacementNamed(context, '/expenses');
        break;
      case 4:
        Navigator.pushReplacementNamed(context, '/profile');
        break;
    }
  }
}
