/// Professional validation utilities
class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }
  
  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    
    return null;
  }
  
  // Name validation
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    
    if (value.length < 2) {
      return 'Name must be at least 2 characters long';
    }
    
    if (value.length > 50) {
      return 'Name cannot exceed 50 characters';
    }
    
    final nameRegex = RegExp(r'^[a-zA-Z\s\u0900-\u097F]+$');
    if (!nameRegex.hasMatch(value)) {
      return 'Name can only contain letters and spaces';
    }
    
    return null;
  }
  
  // Amount validation
  static String? validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Amount is required';
    }
    
    final amount = double.tryParse(value);
    if (amount == null) {
      return 'Please enter a valid amount';
    }
    
    if (amount <= 0) {
      return 'Amount must be greater than 0';
    }
    
    if (amount > 1000000) {
      return 'Amount cannot exceed ₹10,00,000';
    }
    
    return null;
  }
  
  // Receipt number validation
  static String? validateReceiptNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Receipt number is required';
    }
    
    final receiptNo = int.tryParse(value);
    if (receiptNo == null) {
      return 'Please enter a valid receipt number';
    }
    
    if (receiptNo <= 0) {
      return 'Receipt number must be greater than 0';
    }
    
    return null;
  }
  
  // Mobile number validation
  static String? validateMobileNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Mobile number is optional
    }
    
    final mobileRegex = RegExp(r'^[6-9]\d{9}$');
    if (!mobileRegex.hasMatch(value)) {
      return 'Please enter a valid 10-digit mobile number';
    }
    
    return null;
  }
  
  // Required field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }
  
  // Description validation
  static String? validateDescription(String? value) {
    if (value != null && value.length > 200) {
      return 'Description cannot exceed 200 characters';
    }
    return null;
  }
  
  // Year validation
  static String? validateYear(String? value) {
    if (value == null || value.isEmpty) {
      return 'Year is required';
    }
    
    final year = int.tryParse(value);
    if (year == null) {
      return 'Please enter a valid year';
    }
    
    final currentYear = DateTime.now().year;
    if (year < 2000 || year > currentYear + 10) {
      return 'Please enter a valid year between 2000 and ${currentYear + 10}';
    }
    
    return null;
  }
}
