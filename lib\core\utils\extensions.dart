import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Useful extensions for common operations
extension StringExtensions on String {
  /// Capitalize first letter of each word
  String get titleCase {
    return split(' ')
        .map((word) => word.isEmpty 
            ? word 
            : '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}')
        .join(' ');
  }
  
  /// Check if string is a valid email
  bool get isValidEmail {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(this);
  }
  
  /// Check if string is a valid mobile number
  bool get isValidMobile {
    final mobileRegex = RegExp(r'^[6-9]\d{9}$');
    return mobileRegex.hasMatch(this);
  }
  
  /// Remove extra spaces
  String get cleanSpaces {
    return replaceAll(RegExp(r'\s+'), ' ').trim();
  }
}

extension DoubleExtensions on double {
  /// Format currency with Indian rupee symbol
  String get toCurrency {
    final formatter = NumberFormat.currency(
      locale: 'hi_IN',
      symbol: '₹',
      decimalDigits: 2,
    );
    return formatter.format(this);
  }
  
  /// Format currency without decimal places for whole numbers
  String get toCurrencyCompact {
    if (this == truncateToDouble()) {
      return '₹${NumberFormat('#,##,###', 'hi_IN').format(this)}';
    }
    return toCurrency;
  }
}

extension DateTimeExtensions on DateTime {
  /// Format date as dd/MM/yyyy
  String get toDateString {
    return DateFormat('dd/MM/yyyy').format(this);
  }
  
  /// Format date as dd MMM yyyy
  String get toDisplayDate {
    return DateFormat('dd MMM yyyy').format(this);
  }
  
  /// Format date and time as dd/MM/yyyy HH:mm
  String get toDateTimeString {
    return DateFormat('dd/MM/yyyy HH:mm').format(this);
  }
  
  /// Check if date is today
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }
  
  /// Check if date is yesterday
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }
  
  /// Get relative date string (Today, Yesterday, or date)
  String get toRelativeString {
    if (isToday) return 'Today';
    if (isYesterday) return 'Yesterday';
    return toDisplayDate;
  }
}

extension BuildContextExtensions on BuildContext {
  /// Get theme data
  ThemeData get theme => Theme.of(this);
  
  /// Get color scheme
  ColorScheme get colorScheme => theme.colorScheme;
  
  /// Get text theme
  TextTheme get textTheme => theme.textTheme;
  
  /// Get media query
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  
  /// Get screen size
  Size get screenSize => mediaQuery.size;
  
  /// Get screen width
  double get screenWidth => screenSize.width;
  
  /// Get screen height
  double get screenHeight => screenSize.height;
  
  /// Check if device is tablet
  bool get isTablet => screenWidth > 600;
  
  /// Check if device is mobile
  bool get isMobile => screenWidth <= 600;
  
  /// Show snackbar with message
  void showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError 
            ? colorScheme.error 
            : colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
  
  /// Show success snackbar
  void showSuccessSnackBar(String message) {
    showSnackBar(message, isError: false);
  }
  
  /// Show error snackbar
  void showErrorSnackBar(String message) {
    showSnackBar(message, isError: true);
  }
}

extension ListExtensions on List {
  /// Check if list is null or empty
  bool get isNullOrEmpty => isEmpty;
  
  /// Check if list is not null and not empty
  bool get isNotNullOrEmpty => isNotEmpty;
}

extension IntExtensions on int {
  /// Convert to Indian number format
  String get toIndianFormat {
    return NumberFormat('#,##,###', 'hi_IN').format(this);
  }
}
