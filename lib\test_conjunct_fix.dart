import 'package:flutter/material.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/utils/hive_helper.dart';

/// Comprehensive test for conjunct character fixes
/// 
/// This widget tests the PDF generation with various conjunct characters
/// to ensure they render properly in the generated PDF

class ConjunctTestScreen extends StatefulWidget {
  const ConjunctTestScreen({super.key});

  @override
  State<ConjunctTestScreen> createState() => _ConjunctTestScreenState();
}

class _ConjunctTestScreenState extends State<ConjunctTestScreen> {
  bool _isLoading = false;
  String _status = 'Ready to test conjunct characters';
  
  // Test cases with problematic conjunct characters
  final List<Map<String, String>> testCases = [
    {
      'name': 'श्री गणेश मंडळ',
      'description': 'Basic श्री conjunct test',
      'amount': '1100',
      'words': 'एक हजार एकशे रुपये'
    },
    {
      'name': 'क्षत्रिय कुटुंब',
      'description': 'क्ष conjunct test',
      'amount': '2100',
      'words': 'दोन हजार एकशे रुपये'
    },
    {
      'name': 'त्रिकोण आकार',
      'description': 'त्र conjunct test',
      'amount': '3100',
      'words': 'तीन हजार एकशे रुपये'
    },
    {
      'name': 'ज्ञानेश्वर महाराज',
      'description': 'ज्ञ conjunct test',
      'amount': '4100',
      'words': 'चार हजार एकशे रुपये'
    },
    {
      'name': 'स्वतंत्रता दिन',
      'description': 'स्व + त्र conjuncts',
      'amount': '5100',
      'words': 'पाच हजार एकशे रुपये'
    },
    {
      'name': 'प्रसन्न चित्त',
      'description': 'प्र conjunct test',
      'amount': '6100',
      'words': 'सहा हजार एकशे रुपये'
    },
    {
      'name': 'स्थापना दिवस',
      'description': 'स्थ conjunct test',
      'amount': '7100',
      'words': 'सात हजार एकशे रुपये'
    },
    {
      'name': 'न्यायालय व्यवस्था',
      'description': 'न्य conjunct test',
      'amount': '8100',
      'words': 'आठ हजार एकशे रुपये'
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeHive();
  }

  Future<void> _initializeHive() async {
    try {
      await HiveHelper.init();
      
      // Create test profile with conjunct characters
      final profile = Profile(
        mandalName: 'श्री गणेश मंडळ',
        address: 'पुणे, महाराष्ट्र',
        currentYear: '2025',
        mandalRegistrationNo: 'MH/2025/001',
      );
      
      final profileBox = HiveHelper.getProfileBox();
      await profileBox.clear();
      await profileBox.add(profile);
      
      setState(() {
        _status = 'Hive initialized with test profile';
      });
    } catch (e) {
      setState(() {
        _status = 'Error initializing Hive: $e';
      });
    }
  }

  Future<void> _testConjunctCharacters() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing conjunct characters...';
    });

    try {
      for (int i = 0; i < testCases.length; i++) {
        final testCase = testCases[i];
        
        setState(() {
          _status = 'Testing ${i + 1}/${testCases.length}: ${testCase['name']}';
        });
        
        // Create Wargani with conjunct characters
        final wargani = Wargani(
          receiptNo: i + 1,
          name: testCase['name']!,
          amount: double.parse(testCase['amount']!),
          date: DateTime.now(),
          prefix: 'WR',
          mobileNo: '9876543210',
          amountInWords: testCase['words'],
        );
        
        // Generate PDF
        final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
          context,
          wargani,
          'Test User',
        );
        
        if (pdfPath != null) {
          print('✅ Generated PDF for ${testCase['name']}: $pdfPath');
        } else {
          print('❌ Failed to generate PDF for ${testCase['name']}');
        }
        
        // Small delay between tests
        await Future.delayed(const Duration(milliseconds: 500));
      }
      
      setState(() {
        _status = '✅ All conjunct character tests completed!';
        _isLoading = false;
      });
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All PDF tests completed! Check generated files.'),
            backgroundColor: Colors.green,
          ),
        );
      }
      
    } catch (e) {
      setState(() {
        _status = '❌ Error during testing: $e';
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _testSingleConjunct(int index) async {
    if (index >= testCases.length) return;
    
    setState(() {
      _isLoading = true;
      _status = 'Testing single conjunct...';
    });

    try {
      final testCase = testCases[index];
      
      final wargani = Wargani(
        receiptNo: index + 100,
        name: testCase['name']!,
        amount: double.parse(testCase['amount']!),
        date: DateTime.now(),
        prefix: 'WR',
        mobileNo: '9876543210',
        amountInWords: testCase['words'],
      );
      
      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        wargani,
        'Test User',
      );
      
      setState(() {
        _status = pdfPath != null 
            ? '✅ PDF generated: ${testCase['name']}'
            : '❌ Failed to generate PDF';
        _isLoading = false;
      });
      
    } catch (e) {
      setState(() {
        _status = '❌ Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Conjunct Character Test'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                _status,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Test all button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _testConjunctCharacters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.all(16),
                ),
                child: _isLoading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(color: Colors.white),
                          ),
                          SizedBox(width: 10),
                          Text('Testing...', style: TextStyle(color: Colors.white)),
                        ],
                      )
                    : const Text(
                        'Test All Conjunct Characters',
                        style: TextStyle(fontSize: 18, color: Colors.white),
                      ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Individual test cases
            const Text(
              'Individual Test Cases:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            Expanded(
              child: ListView.builder(
                itemCount: testCases.length,
                itemBuilder: (context, index) {
                  final testCase = testCases[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      title: Text(
                        testCase['name']!,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(testCase['description']!),
                          Text('Amount: ₹${testCase['amount']}'),
                          Text('Words: ${testCase['words']}'),
                        ],
                      ),
                      trailing: ElevatedButton(
                        onPressed: _isLoading ? null : () => _testSingleConjunct(index),
                        child: const Text('Test'),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Instructions for using this test:
/// 
/// 1. Add this screen to your app navigation
/// 2. Run the app and navigate to this screen
/// 3. Click "Test All Conjunct Characters" to test all cases
/// 4. Or click individual "Test" buttons for specific cases
/// 5. Check the generated PDF files for proper conjunct rendering
/// 
/// What to look for in PDFs:
/// ✅ श्री should appear as single character (not श + ् + री)
/// ✅ क्ष should appear as single character (not क + ् + ष)
/// ✅ त्र should appear as single character (not त + ् + र)
/// ✅ All conjuncts should be properly formed
/// ✅ No broken or missing characters
/// ✅ Proper spacing and alignment
