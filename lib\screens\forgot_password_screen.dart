import 'package:flutter/material.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/models/user_model.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _secretAnswerController = TextEditingController();
  final _newPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _emailFound = false;
  User? _foundUser;

  Future<void> _findUser() async {
    if (_formKey.currentState!.validate()) {
      final userBox = HiveHelper.getUsersBox();
      final users = userBox.values.where((user) => user.email == _emailController.text);
      if (users.isNotEmpty) {
        setState(() {
          _emailFound = true;
          _foundUser = users.first;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.userNotFound)),
        );
      }
    }
  }

  Future<void> _resetPassword() async {
    if (_formKey.currentState!.validate()) {
      if (_foundUser!.secretAnswer == _secretAnswerController.text) {
        setState(() {
          _isLoading = true;
        });
        final userBox = HiveHelper.getUsersBox();
        final userKey = userBox.keyAt(userBox.values.toList().indexOf(_foundUser!));
        final updatedUser = User(
          uid: _foundUser!.uid,
          name: _foundUser!.name,
          email: _foundUser!.email,
          password: _newPasswordController.text,
          secretQuestion: _foundUser!.secretQuestion,
          secretAnswer: _foundUser!.secretAnswer,
        );
        await userBox.put(userKey, updatedUser);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.passwordResetSuccess)),
        );
        Navigator.pop(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Incorrect secret answer')),
        );
      }

      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _secretAnswerController.dispose();
    _newPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.forgotPassword),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    labelText: localizations.email,
                    prefixIcon: const Icon(Icons.email),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations.pleaseEnterEmail;
                    }
                    return null;
                  },
                  readOnly: _emailFound,
                ),
                const SizedBox(height: 20),
                if (_emailFound) ...[
                  Text(
                    _foundUser!.secretQuestion,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  TextFormField(
                    controller: _secretAnswerController,
                    decoration: InputDecoration(
                      labelText: localizations.secretAnswer,
                      prefixIcon: const Icon(Icons.question_answer),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return localizations.pleaseEnterSecretAnswer;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: _newPasswordController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: localizations.newPassword,
                      prefixIcon: const Icon(Icons.lock),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return localizations.pleaseEnterNewPassword;
                      }
                      return null;
                    },
                  ),
                ],
                const SizedBox(height: 30),
                _isLoading
                    ? const CircularProgressIndicator()
                    : SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _emailFound ? _resetPassword : _findUser,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 15),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: Text(
                            _emailFound ? localizations.resetPassword : localizations.findUser,
                            style: const TextStyle(fontSize: 18),
                          ),
                        ),
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
