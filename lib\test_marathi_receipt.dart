import 'package:flutter/material.dart';
import 'package:wargani/marathi_receipt_generator.dart';

/// Test app for Marathi Receipt Generator
/// 
/// This app demonstrates the generation of professional Marathi receipts
/// with proper Unicode Devanagari font support

class TestMarathiReceiptApp extends StatelessWidget {
  const TestMarathiReceiptApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'मराठी पावती जनरेटर',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        fontFamily: 'Roboto',
      ),
      home: const MarathiReceiptScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// Main function to run the test app
void main() {
  runApp(const TestMarathiReceiptApp());
}

/// Standalone function to generate sample Marathi receipt
/// 
/// Usage:
/// ```dart
/// final pdfPath = await generateSampleMarathiReceipt();
/// print('Receipt generated at: $pdfPath');
/// ```
Future<String?> generateSampleMarathiReceipt() async {
  try {
    print('🧾 Generating sample Marathi receipt...');
    
    final pdfPath = await MarathiReceiptGenerator.generateMarathiReceipt(
      title: '|| श्री गणेश पूजन ||',
      organizationName: 'श्री गणेश मंडळ',
      address: 'पुणे, महाराष्ट्र',
      receiptNumber: 4567,
      donorName: 'श्री राम शर्मा',
      amount: 4545.0,
      date: DateTime.now(),
      mobileNumber: '9876543210',
      purpose: 'गणेश उत्सव दान',
    );
    
    if (pdfPath != null) {
      print('✅ Sample receipt generated successfully at: $pdfPath');
    } else {
      print('✅ Sample receipt opened in browser (Web platform)');
    }
    
    return pdfPath;
    
  } catch (e) {
    print('❌ Error generating sample receipt: $e');
    rethrow;
  }
}

/// Test function to verify Marathi number conversion
void testMarathiNumberConversion() {
  print('🔢 Testing Marathi number conversion...');
  
  final testCases = [
    1.0, 5.0, 10.0, 25.0, 100.0, 500.0, 1000.0, 1100.0, 
    2500.0, 4545.0, 10000.0, 25000.0, 50000.0, 100000.0
  ];
  
  print('📝 Number conversion test cases:');
  for (final amount in testCases) {
    // Note: This would require access to the private method
    // In actual implementation, you might want to make it public for testing
    print('₹${amount.toInt()} → [Marathi conversion would appear here]');
  }
  
  print('✅ Number conversion tests prepared. Generate receipt to verify.');
}

/// Test function to verify conjunct character rendering
void testConjunctCharactersInReceipt() {
  print('🧪 Testing conjunct characters in receipt context...');
  
  final testCases = [
    '|| श्री गणेश पूजन ||',
    'श्री गणेश मंडळ',
    'क्षत्रिय कुटुंब',
    'त्रिकोण आकार',
    'ज्ञानेश्वर महाराज',
    'स्वतंत्रता दिन',
    'प्रसन्न चित्त',
    'स्थापना दिवस',
    'न्यायालय व्यवस्था',
    'गणेश उत्सव दान',
    'आपल्या दानाबद्दल धन्यवाद!',
    'रोख मिळाले...!',
  ];
  
  print('📝 Conjunct character test cases for receipt:');
  for (int i = 0; i < testCases.length; i++) {
    print('${i + 1}. ${testCases[i]}');
  }
  
  print('✅ All conjunct test cases prepared. Generate receipt to verify rendering.');
}

/// Quick test widget for standalone testing
class QuickReceiptTestWidget extends StatefulWidget {
  const QuickReceiptTestWidget({super.key});

  @override
  State<QuickReceiptTestWidget> createState() => _QuickReceiptTestWidgetState();
}

class _QuickReceiptTestWidgetState extends State<QuickReceiptTestWidget> {
  String _testResults = 'Ready to run tests';
  bool _isGenerating = false;

  void _runConjunctTests() {
    setState(() {
      _testResults = 'Running conjunct character tests...';
    });

    testConjunctCharactersInReceipt();

    setState(() {
      _testResults = '✅ Conjunct character tests completed. Check console for details.';
    });
  }

  void _runNumberTests() {
    setState(() {
      _testResults = 'Running Marathi number conversion tests...';
    });

    testMarathiNumberConversion();

    setState(() {
      _testResults = '✅ Number conversion tests completed. Check console for details.';
    });
  }

  Future<void> _generateSampleReceipt() async {
    setState(() {
      _testResults = 'Generating sample receipt...';
      _isGenerating = true;
    });

    try {
      final pdfPath = await generateSampleMarathiReceipt();
      setState(() {
        _testResults = pdfPath != null 
            ? '✅ Sample receipt generated: $pdfPath'
            : '✅ Sample receipt opened in browser';
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _testResults = '❌ Sample receipt generation failed: $e';
        _isGenerating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marathi Receipt Test'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'मराठी पावती जनरेटर टेस्ट',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Test Results:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(_testResults),
            ),
            
            const SizedBox(height: 30),
            
            // Test buttons
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _runConjunctTests,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.all(15),
                ),
                child: const Text(
                  'Test Conjunct Characters',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _runNumberTests,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  padding: const EdgeInsets.all(15),
                ),
                child: const Text(
                  'Test Number Conversion',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            const SizedBox(height: 15),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isGenerating ? null : _generateSampleReceipt,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.all(15),
                ),
                child: _isGenerating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(color: Colors.white),
                          ),
                          SizedBox(width: 10),
                          Text('Generating...', style: TextStyle(color: Colors.white)),
                        ],
                      )
                    : const Text(
                        'Generate Sample Receipt',
                        style: TextStyle(color: Colors.white),
                      ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Instructions
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 10),
            
            const Text(
              '1. Test conjunct characters to verify processing\n'
              '2. Test number conversion for Marathi words\n'
              '3. Generate sample receipt to verify PDF output\n'
              '4. Check generated PDF for proper rendering\n'
              '5. Verify all Marathi text appears correctly',
              style: TextStyle(fontSize: 14),
            ),
            
            const SizedBox(height: 20),
            
            // Sample data display
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sample Receipt Data:',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                  SizedBox(height: 5),
                  Text(
                    'Title: || श्री गणेश पूजन ||\n'
                    'Organization: श्री गणेश मंडळ\n'
                    'Donor: श्री राम शर्मा\n'
                    'Amount: ₹4545 (चार हजार पाचशे पंचेचाळीस रुपये फक्त)\n'
                    'Purpose: गणेश उत्सव दान',
                    style: TextStyle(fontSize: 12, color: Colors.orange),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Instructions for using this test:
/// 
/// 1. **Run the main app:**
///    ```bash
///    flutter run lib/test_marathi_receipt.dart
///    ```
/// 
/// 2. **Generate sample receipt programmatically:**
///    ```dart
///    final pdfPath = await generateSampleMarathiReceipt();
///    ```
/// 
/// 3. **What to verify in the generated receipt:**
///    - ✅ Title: || श्री गणेश पूजन || renders correctly
///    - ✅ All Marathi text appears without broken characters
///    - ✅ Conjunct characters (श्री, क्ष, त्र) appear as single units
///    - ✅ Amount in words: चार हजार पाचशे पंचेचाळीस रुपये फक्त
///    - ✅ Thank you note: आपल्या दानाबद्दल धन्यवाद!
///    - ✅ Professional formatting with proper spacing
/// 
/// 4. **Expected receipt content:**
///    - Orange header with title and organization name
///    - Receipt number and date
///    - Donor name with proper Marathi prefix (सो.श्री)
///    - Amount in digits and Marathi words
///    - Mobile number and purpose (if provided)
///    - Thank you note and signature section
///    - Footer with Unicode compliance note
/// 
/// 5. **Font verification:**
///    - PDF should use embedded Noto Sans Devanagari font
///    - All conjunct characters should render properly
///    - No missing or broken characters in any PDF viewer
