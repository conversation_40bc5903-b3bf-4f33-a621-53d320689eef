{"@@locale": "en", "appTitle": "Wargani", "developedBy": "Developed by AMSSoftX | https://amssoftx.com", "profile": "Profile", "mandalName": "Mandal Name", "address": "Address", "logo": "Logo", "currentYear": "Current Year", "save": "Save", "dashboard": "Dashboard", "totalWargani": "Total Wargani", "totalDonations": "Total Donations", "totalExpenses": "Total Expenses", "warganiReceipt": "Wargani Receipt", "receiptNo": "Receipt No.", "date": "Date", "name": "Name", "amount": "Amount", "amountInWords": "Amount in Words", "generatePdf": "Generate PDF", "share": "Share", "download": "Download", "thankYouNote": "Thank you for your contribution.", "expenses": "Expenses", "title": "Title", "description": "Description", "addExpense": "Add Expense", "donations": "Donations", "donorName": "Donor Name", "reason": "Reason (Optional)", "addDonation": "Add Donation", "prefix": "Prefix (e.g., Mr./Mrs.)", "warganiSummary": "<PERSON>gan<PERSON> Summary", "donationSummary": "Donation Summary", "expensesSummary": "Expenses Summary", "totalPeople": "Total People", "totalAmount": "Total Amount", "login": "<PERSON><PERSON>", "email": "Email", "password": "Password", "pleaseEnterEmail": "Please enter your email", "pleaseEnterPassword": "Please enter your password", "noUserFound": "No user found for that email.", "wrongPassword": "Wrong password provided for that user.", "loginFailed": "<PERSON><PERSON> failed: {errorMessage}", "@loginFailed": {"placeholders": {"errorMessage": {}}}, "appName": "Wargani", "dontHaveAccount": "Don't have an account? Sign up", "forgotPassword": "Forgot Password?", "forgotPasswordMessage": "Forgot password functionality is not yet implemented.", "signUp": "Sign Up", "signUpSuccess": "Account created successfully!", "weakPassword": "The password provided is too weak.", "emailAlreadyInUse": "The account already exists for that email.", "signUpFailed": "Sign up failed: {errorMessage}", "@signUpFailed": {"placeholders": {"errorMessage": {}}}, "createAccount": "Create New Account", "pleaseEnterName": "Please enter a name", "alreadyHaveAccount": "Already have an account? <PERSON>gin", "cancel": "Cancel", "sendResetLink": "Send Reset Link", "passwordResetEmailSent": "Password reset email sent. Check your inbox.", "passwordResetFailed": "Password reset failed: {errorMessage}", "@passwordResetFailed": {"placeholders": {"errorMessage": {}}}, "userNotFound": "User not found", "passwordResetSuccess": "Password reset successfully", "newPassword": "New Password", "pleaseEnterNewPassword": "Please enter new password", "resetPassword": "Reset Password", "findUser": "Find User", "pleaseEnterMandalName": "Please enter Mandal Name", "pleaseEnterAddress": "Please enter Address", "pleaseEnterCurrentYear": "Please enter Current Year", "profileSaved": "Profile Saved", "userName": "User Name", "userEmail": "User Email", "pleaseEnterReceiptNo": "Please enter a receipt number", "pleaseEnterPrefix": "Please enter a prefix", "pleaseEnterAmount": "Please enter an amount", "pleaseEnterValidAmount": "Please enter a valid amount", "pleaseEnterValidNumber": "Please enter a valid number", "pleaseEnterRegistrationNo": "Please enter a registration number", "pleaseEnterAmountInWords": "Please enter amount in words", "pdfGenerated": "PDF Generated", "pdfGeneratedSuccessfully": "PDF has been generated successfully.", "ok": "OK", "saveReceipt": "Save Receipt", "clearForm": "Clear Form", "preview": "Preview", "mobileNo": "Mobile No.", "generatedBy": "Generated By", "pleaseEnterDonorName": "Please enter a donor name", "noDonationsYet": "No donations yet.", "pleaseEnterTitle": "Please enter a title", "noExpensesYet": "No expenses yet.", "downloadAllExpenses": "Download All Expenses", "shareAllExpenses": "Share All Expenses", "netBalance": "Net Balance", "selectLanguage": "Select Language", "shreeGaneshPrasanna": "|| श्री गणेश प्रसन्न ||", "registrationNo": "Registration No.", "ganeshotsavYear": "<PERSON><PERSON><PERSON><PERSON><PERSON> {year}", "@ganeshotsavYear": {"placeholders": {"year": {}}}, "from": "From", "cashReceived": "Cash Received...!", "thankYou": "Thank You ...!", "signature": "Signature", "logout": "Logout", "secretQuestion": "Secret Question", "pleaseEnterSecretQuestion": "Please enter a secret question", "secretAnswer": "Secret Answer", "pleaseEnterSecretAnswer": "Please enter a secret answer"}