import 'package:wargani/utils/hive_helper.dart';

/// Translation service for converting English text to Marathi/Hindi
class TranslationService {
  // Common English to Marathi translations
  static const Map<String, String> _englishToMarathi = {
    // Names and titles
    'mr': 'श्री',
    'mrs': 'श्रीमती',
    'miss': 'कु.',
    'dr': 'डॉ.',
    'prof': 'प्रा.',

    // Common words
    'ganesh': 'गणेश',
    'ganapati': 'गणपती',
    'festival': 'उत्सव',
    'mandal': 'मंडळ',
    'donation': 'दान',
    'expense': 'खर्च',
    'receipt': 'पावती',
    'amount': 'रक्कम',
    'date': 'दिनांक',
    'name': 'नाव',
    'address': 'पत्ता',
    'mobile': 'मोबाईल',
    'email': 'ईमेल',
    'password': 'पासवर्ड',
    'login': 'लॉगिन',
    'signup': 'साइन अप',
    'save': 'जतन करा',
    'cancel': 'रद्द करा',
    'edit': 'संपादन',
    'delete': 'हटवा',
    'search': 'शोधा',
    'filter': 'फिल्टर',
    'total': 'एकूण',
    'balance': 'शिल्लक',
    'income': 'उत्पन्न',
    'year': 'वर्ष',
    'month': 'महिना',
    'day': 'दिवस',
    'today': 'आज',
    'yesterday': 'काल',
    'tomorrow': 'उद्या',

    // Festival related
    'ganesh utsav': 'गणेश उत्सव',
    'ganpati bappa': 'गणपती बाप्पा',
    'morya': 'मोरया',
    'mangal murti': 'मंगल मूर्ती',
    'vinayak': 'विनायक',

    // Numbers
    'one': 'एक',
    'two': 'दोन',
    'three': 'तीन',
    'four': 'चार',
    'five': 'पाच',
    'six': 'सहा',
    'seven': 'सात',
    'eight': 'आठ',
    'nine': 'नऊ',
    'ten': 'दहा',
    'hundred': 'शंभर',
    'thousand': 'हजार',
    'lakh': 'लाख',
    'crore': 'कोटी',
  };

  // Common English to Hindi translations
  static const Map<String, String> _englishToHindi = {
    // Names and titles
    'mr': 'श्री',
    'mrs': 'श्रीमती',
    'miss': 'कु.',
    'dr': 'डॉ.',
    'prof': 'प्रो.',

    // Common words
    'ganesh': 'गणेश',
    'ganapati': 'गणपति',
    'festival': 'उत्सव',
    'mandal': 'मंडल',
    'donation': 'दान',
    'expense': 'खर्च',
    'receipt': 'रसीद',
    'amount': 'राशि',
    'date': 'दिनांक',
    'name': 'नाम',
    'address': 'पता',
    'mobile': 'मोबाइल',
    'email': 'ईमेल',
    'password': 'पासवर्ड',
    'login': 'लॉगिन',
    'signup': 'साइन अप',
    'save': 'सेव करें',
    'cancel': 'रद्द करें',
    'edit': 'संपादित करें',
    'delete': 'हटाएं',
    'search': 'खोजें',
    'filter': 'फिल्टर',
    'total': 'कुल',
    'balance': 'बैलेंस',
    'income': 'आय',
    'year': 'साल',
    'month': 'महीना',
    'day': 'दिन',
    'today': 'आज',
    'yesterday': 'कल',
    'tomorrow': 'कल',

    // Festival related
    'ganesh utsav': 'गणेश उत्सव',
    'ganpati bappa': 'गणपति बप्पा',
    'morya': 'मोरया',
    'mangal murti': 'मंगल मूर्ति',
    'vinayak': 'विनायक',

    // Numbers
    'one': 'एक',
    'two': 'दो',
    'three': 'तीन',
    'four': 'चार',
    'five': 'पांच',
    'six': 'छह',
    'seven': 'सात',
    'eight': 'आठ',
    'nine': 'नौ',
    'ten': 'दस',
    'hundred': 'सौ',
    'thousand': 'हजार',
    'lakh': 'लाख',
    'crore': 'करोड़',
  };

  /// Get current app language
  static String getCurrentLanguage() {
    return HiveHelper.getLocale() ?? 'en';
  }

  /// Translate English text to current language
  static String translateText(String englishText) {
    final currentLang = getCurrentLanguage();

    if (currentLang == 'en') {
      return englishText;
    }

    final text = englishText.toLowerCase().trim();

    if (currentLang == 'mr') {
      return _englishToMarathi[text] ?? englishText;
    } else if (currentLang == 'hi') {
      return _englishToHindi[text] ?? englishText;
    }

    return englishText;
  }

  /// Translate multiple words in a sentence
  static String translateSentence(String englishSentence) {
    final currentLang = getCurrentLanguage();

    if (currentLang == 'en') {
      return englishSentence;
    }

    String translatedSentence = englishSentence;
    final translationMap = currentLang == 'mr' ? _englishToMarathi : _englishToHindi;

    // Replace each word if translation exists
    translationMap.forEach((english, translated) {
      final regex = RegExp(r'\b' + english + r'\b', caseSensitive: false);
      translatedSentence = translatedSentence.replaceAll(regex, translated);
    });

    return translatedSentence;
  }

  /// Smart translate - handles names, common words, and phrases
  static String smartTranslate(String input) {
    if (input.isEmpty) return input;

    final currentLang = getCurrentLanguage();
    if (currentLang == 'en') return input;

    // First try direct translation
    String result = translateText(input);

    // If no direct translation, try sentence translation
    if (result == input) {
      result = translateSentence(input);
    }

    return result;
  }

  /// Check if text contains English characters
  static bool containsEnglish(String text) {
    return RegExp(r'[a-zA-Z]').hasMatch(text);
  }

  /// Auto-suggest translation while typing
  static String autoSuggestTranslation(String input) {
    if (input.isEmpty || !containsEnglish(input)) {
      return input;
    }

    return smartTranslate(input);
  }
}